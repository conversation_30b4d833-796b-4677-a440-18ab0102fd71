#ifndef NAVIGATION_MOWER_HPP
#define NAVIGATION_MOWER_HPP

#include "data_type.hpp"
#include "iceoryx_publisher_mower.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "mower_msgs/srv/explore_map_result.hpp"
#include "mower_msgs/srv/undock_result.hpp"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "stuck_detection_recovery.hpp"
#include "velocity_publisher.hpp"

#include <atomic>
#include <chrono>
#include <cmath>
#include <condition_variable>
#include <functional>
#include <future>
#include <memory>
#include <mutex>
#include <queue>
#include <sys/prctl.h>
#include <thread>
#include <unordered_map>

namespace fescue_iox
{

struct MowerAlgParam
{
    // 信标调度 - 出桩
    bool is_enable_unstake_mode{true}; // 是否开启出桩模式 /*param*/
    float unstake_distance{1.0};       // 出桩距离 /*param*/
    float unstake_adjust_yaw{0.52};    // 0.52（30） 出桩调整角度 /*param*/
    float unstake_vel_linear{0.2};     // 出桩线速度 /*param*/
    float unstake_vel_angular{0.5};    // 出桩角速度 /*param*/

    // 算法参数
    float mower_linear{0.2};  /*param*/
    float mower_angular{0.5}; /*param*/
    // int perception_drive_cooldown_time{5};   // 感知驱动冷却时间 5s /*param*/
    int edge_mode_direction{-1};             // 默认逆时针 -1 /*param*/
    float cross_region_adjust_yaw{1.57};     // 跨区域后调整方位角 /*param*/
    float cross_region_adjust_displace{0.3}; // 跨区域后调整位移 /*param*/
    float mark_distance_threshold{0.5};      // 1.0/0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    float camera_2_center_dis{0.37};         // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/

    // 新增参数
    int edge_perception_drive_cooldown_time_threshold{10}; // 10s 沿边感知驱动冷却时间  /*param*/
    int qr_detection_cooldown_time_threshold{30};          // 60s 沿边感知驱动冷却时间  /*param*/
    int mark_detection_cooldown_time_threshold{30};        // 60s 沿边感知驱动冷却时间  /*param*/

    // 区域探索
    float recharge_distance_threshold{1.2}; // 区域探索回充距离阈值/*param*/

    // 割草前处理
    float mower_start_qr_distance_threshold{0.5}; // 割草前与二维码的距离阈值/*param*/
};

// 区域探索结果
struct RegionExploreResult
{
    bool result;        // 区域探索结果
    uint64_t timestamp; // 时间戳,毫秒
    mower_msgs::srv::MapResult master_region_map_result;
    mower_msgs::srv::MapResult slave_region_map_result;
};

struct MowerAlgResult
{
    bool mower_completed{false};
    MowerAlgResult() = default;
    MowerAlgResult(bool mower_completed)
        : mower_completed(mower_completed)
    {
    }
};

class NavigationMowerAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationMowerAlg(const MowerAlgParam &param);
    ~NavigationMowerAlg();
    void ProhibitVelPublisher();
    const char *GetVersion();
    void DataConversion(MarkLocationResult &mark_loc_result);
    MowerAlgResult Run(const MarkLocationResult &mark_loc_result,
                       CrossRegionRunningState cross_region_state,
                       const QRCodeLocationResult &qrcode_loc_result,
                       const PerceptionFusionResult &fusion_result,
                       RechargeRunningState recharge_state,
                       McuExceptionStatus &mcu_exception_status,
                       BehaviorRunningState &behavior_state);

    void HandleSelfCheckAndOperation(const MarkLocationResult &mark_loc_result,
                                     CrossRegionRunningState cross_region_state,
                                     const QRCodeLocationResult &qrcode_loc_result,
                                     const PerceptionFusionResult &fusion_result,
                                     RechargeRunningState recharge_state,
                                     McuExceptionStatus &mcu_exception_status,
                                     BehaviorRunningState &behavior_state);

    void NormalOperation(const MarkLocationResult &mark_loc_result,
                         CrossRegionRunningState cross_region_state,
                         const QRCodeLocationResult &qrcode_loc_result,
                         const PerceptionFusionResult &fusion_result,
                         RechargeRunningState recharge_state,
                         McuExceptionStatus &mcu_exception_status,
                         BehaviorRunningState &behavior_state);

    void NormalMowingModule(const MarkLocationResult &mark_loc_result,
                            CrossRegionRunningState cross_region_state,
                            const QRCodeLocationResult &qrcode_loc_result,
                            const PerceptionFusionResult &fusion_result,
                            RechargeRunningState recharge_state,
                            McuExceptionStatus &mcu_exception_status,
                            BehaviorRunningState &behavior_state);

    void RegionExplorationModule(const MarkLocationResult &mark_loc_result,
                                 CrossRegionRunningState cross_region_state,
                                 const QRCodeLocationResult &qrcode_loc_result,
                                 const PerceptionFusionResult &fusion_result,
                                 RechargeRunningState recharge_state,
                                 McuExceptionStatus &mcu_exception_status,
                                 BehaviorRunningState &behavior_state);

    void CutBorderModule(const MarkLocationResult &mark_loc_result,
                         CrossRegionRunningState cross_region_state,
                         const QRCodeLocationResult &qrcode_loc_result,
                         const PerceptionFusionResult &fusion_result,
                         RechargeRunningState recharge_state,
                         McuExceptionStatus &mcu_exception_status,
                         BehaviorRunningState &behavior_state);

    /**区域探索功能处理函数*/
    void PerformExploration(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                            CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state);
    void ProcessExplorationUnstakeMode();
    void ProcessExplorationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void ProcessBeaconDetection(const MarkLocationResult &mark_loc_result,
                                bool &enter_multi_region_exploration,
                                bool &is_beacon_valid);
    void ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                          const bool &enter_multi_region_exploration);
    void ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                         const bool &enter_multi_region_exploration,
                                         bool &is_beacon_valid);
    void HandleExplorationMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state);
    void ProcessExplorationRechargeException(RechargeRunningState recharge_state);
    void ProcessExplorationCrossRegionException(CrossRegionRunningState cross_region_state);

    void HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active);
    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold);
    void ResetAndActivateCooldown();
    void ShowExplorePrint(BehaviorRunningState &behavior_state);
    int PairNumber(int n);
    void ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result);
    /**区域探索功能处理函数*/

    void HandleMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state);
    void ProcessRechargeException(RechargeRunningState recharge_state);
    void ProcessCrossRegionException(CrossRegionRunningState cross_region_state);
    void ProcessRecoveryException();
    void HandleNormalOperation(CrossRegionRunningState cross_region_state, RechargeRunningState recharge_state,
                               BehaviorRunningState &behavior_state);
    void ProcessNormalOperationUnstakeMode();
    void ProcessNormalOperationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void ProcessRandomMowing();
    void ProcessSpiralMowing();
    void ProcessCrossRegionMode(CrossRegionRunningState cross_region_state);
    void ProcessRechargeMode(RechargeRunningState recharge_state);

    void ResetMowerAlgFlags();
    void SetMowerAlgParam(const MowerAlgParam &param);
    void SetRandomMowerRunningState(RandomMowerRunningState state);
    void SetMowerRunningState(MowerRunningState state);
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetMarkLocationMarkIdCallback(std::function<bool(int)> callback);
    void SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback);
    void SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback);
    void SetUndockResultCallback(std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> callback);
    void SetAreaCalcStartCallback(std::function<bool(uint64_t)> callback);
    void SetAreaCalcStopCallback(std::function<bool(uint64_t, float &, float &)> callback);
    void SetRegionExploreResultCallback(std::function<void(RegionExploreResult &)> callback);
    void SetCutBorderResultCallback(std::function<void(bool, bool)> callback);
    void SetPerceptionLocalizationAlgCtrlCallback(std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> callback);
    void SetAppTriggersRecharge(bool is_recharge);
    void SetAppTriggersCrossRegion(bool is_cross_region);
    void SetAppTriggersCutBorder(bool is_cut_border);
    void SetIsOnDocker(bool is_on_docker) { is_on_docker_ = is_on_docker; }
    void SetMCUSensor(const mower_msgs::msg::McuSensor &data);
    void SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }

    void SetChargePileDockStatus(bool status);
    void SetMowerComplete(const bool &mower_completed);
    void SetAppTriggersMower(bool is_mower);
    void SetAppTriggersSpiralMower(bool is_mower);
    void SetAppTriggersRegionExplore(bool is_region_explore);
    void SetGrassDetecteStatus(const GrassDetectStatus &data);
    void SetAllTaskClose();

    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result);
    void SetImuData(const ImuData &imu_data);
    bool IsWheelSlipping(const MotorSpeedData &motor_data,
                         const MotionDetectionResult &motion_detection_result,
                         float wheel_radius, float wheel_base);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);

    // 脱困检测和恢复功能
    bool IsStuckDetected();
    bool StartStuckRecovery();
    void StopStuckRecovery();
    bool IsStuckRecoveryActive();
    void SetStuckDetectionActive(bool active);

private:
    void InitPublisher();
    void PublishSlipException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

    void ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result);
    void CheckVelocityAndUpdateState(BehaviorRunningState &behavior_state);

    void UpdateFeatureSelection(const ThreadControl &thread_control);
    void DealFeatureSelect(ThreadControl control, bool state);
    void EdgeFollowDisable();
    void EdgeFollowEnable();
    void CrossRegionDisable();
    void CrossRegionEnable();

    void UpdateCrossRegionRunningState(CrossRegionRunningState state);
    void UpdateRechargeRunningState(RechargeRunningState state);

    void SetUndockResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status = mower_msgs::srv::UndockOperationStatus::INTERRUPTIBLE);

    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result);

    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);

    bool SetMarkLocationMarkId(int mark_id);

    void ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular);

    void ControlLinearMotion(const float &pass_point, const float &location, const float &vel_linear, const int &reverse);

    void PerformUnstakeModeAsync(const QRCodeLocationResult &qrcode_loc_result);
    void PerformUnstakeMode();
    void PerformUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void PreProcessingMowing(const QRCodeLocationResult &qrcode_loc_result);
    void ProcessQRcodeAndUnstake(const std::vector<float> &qrcode_result);
    std::vector<float> Collect_QRdata();
    std::vector<float> Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set);
    void HandleRecoveryStart();
    void HandleRecoveryEnd();
    void ProcessRecoverySlipException();

    void PerformRandomMowing();
    void PerformSpiralMowing();

    void HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state);
    void SetSlippingStatus(bool is_slipping);

    // 新增
    bool IsGrassField(const GrassDetectStatus &grass_detect_status);
    bool SelfChecking();
    bool PerformSelfCheckRecovery();

    // cut border
    void ProcessCutBorderUnstakeMode();
    void ProcessCutBorderUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void HandleCutBorderMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state);
    void ProcessCutBorderRechargeException(RechargeRunningState recharge_state);
    void ProcessCutBorderCrossRegionException(CrossRegionRunningState cross_region_state);
    void ProcessSingleAreaCutBorderMode(const QRCodeLocationResult &qrcode_loc_result,
                                        const bool &enter_multi_region_exploration);
    void ProcessMultiAreaCutBorderMode(const MarkLocationResult &mark_loc_result,
                                       const bool &enter_multi_region_exploration,
                                       bool &is_beacon_valid);
    void HandleCutBorderCrossRegionStates(CrossRegionRunningState &cross_region_state);
    void ProcessingCutBorderRecharge(const QRCodeLocationResult &qrcode_loc_result);
    void TriggerExceptionPublishing();
    void PerformCutBorder(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                          CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state);

    // Test
    void Test(CrossRegionRunningState cross_region_state);

private:
    void InitSlipDetection();
    void DeinitSlipDetection();
    void SlipDetectionThread();

    // 脱困检测和恢复
    void InitStuckDetectionRecovery();
    void DeinitStuckDetectionRecovery();

private:
    std::chrono::steady_clock::time_point slip_start_time_;
    std::atomic_bool is_continuous_slipping_{false};

    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    std::mutex motion_detection_result_mtx_;
    MotionDetectionResult motion_detection_result_;

    std::thread slip_detection_thread_;
    std::atomic_bool slip_detection_running_{false};
    std::atomic_bool is_slipping_detected_{false};
    // int slip_detection_frequency_{20}; // 检测频率(Hz)
    float wheel_radius_ = 0.1f;     // 车轮半径 (米)
    float min_valid_linear_ = 0.1f; // 最小有效速度 (米/秒)
    float min_valid_angular_ = 0.2f;
    float wheel_base_ = 0.335f;

    // 脱困检测和恢复
    std::unique_ptr<StuckDetectionRecovery> stuck_detection_recovery_;
    std::mutex imu_data_mtx_;
    ImuData imu_data_;

private:
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;
    std::function<bool(int)> set_mark_id_callback_;
    std::function<void(CrossRegionRunningState)> cross_region_running_state_callback_;
    std::function<void(RechargeRunningState)> recharge_running_state_callback_;
    std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> undock_result_callback_;
    std::function<bool(uint64_t)> area_calc_start_callback_;
    std::function<bool(uint64_t, float &, float &)> area_calc_stop_callback_;
    std::function<void(RegionExploreResult &)> region_explore_result_callback_;
    std::function<void(bool, bool)> cut_border_result_callback_;
    std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> perception_localization_alg_ctrl_callback_{nullptr};

    std::unique_ptr<iox_exception_publisher> pub_exception_;
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_iceoryx_exception_;

    std::mutex mark_loc_mtx_;
    MarkLocationResult mark_loc_result;
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};

    // 运行状态量
    bool is_on_docker_{false};
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-未开始，1-运行中，2-暂停
    ThreadControl thread_control_{ThreadControl::UNDEFINED};         // 控制其它任务

    bool is_unstake_mode_completed_ = false; // 第一阶段，出桩模式是否完成
    bool is_unstake_success_ = false;        // 出桩是否成功
    bool is_power_connected_ = false;        // True 表示已接电，False 表示未接电

    bool mcu_triggers_cross_region_ = false;       // MCU 触发跨区线程
    bool mcu_triggers_recharge_ = false;           // MCU 触发回充线程
    bool mcu_triggers_mower_ = false;              // MCU 触发割草线程
    bool mcu_triggers_region_exploration_ = false; // MCU 触发探索线程
    bool mcu_triggers_spiral_mower_ = false;       // MCU 触发螺旋割草线程
    bool mcu_triggers_cut_border_ = false;         // MCU 触发边切线程

    bool mower_completed_ = false; // 割草是否完成
    std::deque<GrassDetectStatus> frames_;
    GrassDetectStatus grass_detect_status_;
    bool is_on_grass_field_ = false; // 是否在草地上

    bool is_region_explore_mode_start_{false};
    bool is_cut_border_mode_start_{false};

    /**区域探索功能处理函数*/
    bool is_cooldown_active_ = false; // 用于控制冷却机制是否激活
    bool is_first_enter_last_cooldown_time_ = true;
    std::chrono::steady_clock::time_point last_cooldown_time_; // 用于记录冷却开始时间
    std::chrono::seconds edge_perception_drive_duration_{0};

    bool enter_multi_region_exploration_ = false; // 是否进入多区域探索

    // 单区域
    int qr_code_detection_count_{1}; // 充电桩二维码检测次数
    bool is_first_enter_explore_last_qr_detection_time_ = true;
    std::chrono::steady_clock::time_point last_qr_explore_detection_time_; // 上次检测到充电桩二维码的时间
    std::chrono::seconds qr_detection_duration_{0};
    bool is_single_area_recharge_ = false;

    // 多区域
    BeaconStatus beacon_status_;
    int current_mark_id_{-1};
    bool is_first_enter_last_mark_detection_time_ = true;
    std::chrono::steady_clock::time_point last_mark_detection_time_; // 上次检测到充电桩二维码的时间
    std::chrono::seconds mark_detection_duration_{0};

    // 新增
    bool first_detection_beacon_ = true; // 首次检测信标
    int next_paired_beacon_id_ = -1;     // 下一对信标id

    int region_count_{1};

    // 区域探索获取信息
    mower_msgs::srv::MapResult master_region_explore_result_;
    mower_msgs::srv::MapResult slave_region_explore_result_;
    bool is_master_region_ = true;
    bool is_first_region_explore_mode_end_ = true;
    /**区域探索功能处理函数*/

    /**割草模式非草地异常检测*/
    bool is_first_non_grass_detection_{true};
    std::chrono::steady_clock::time_point last_grass_time_;
    std::chrono::seconds non_grass_duration_{0};
    static constexpr int NON_GRASS_WARN_THRESHOLD_SECONDS{5};   // 5秒阈值
    static constexpr int NON_GRASS_ERROR_THRESHOLD_SECONDS{30}; // 30秒阈值

    std::chrono::steady_clock::time_point exception_start_time_;
    bool is_publishing_exception_{false};
    /**割草模式非草地异常检测*/

    /**自检模式*/
    bool is_self_checking_{false};                                  // 是否正在进行自检
    std::chrono::steady_clock::time_point self_check_start_time_;   // 自检开始时间
    static constexpr int SELF_CHECK_DURATION_SECONDS{2};            // 自检持续时间 2 秒
    static constexpr int SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS{1}; // 1秒阈值
    bool is_self_success_{false};
    bool is_self_recovery_active_{false};    // 是否正在进行自检恢复
    float self_recovery_distance_{0.5};      // 自检恢复回退距离(m)/*param*/
    float self_recovery_linear_speed_{-0.1}; // 自检恢复回退速度(m/s)/*param*/
    /**自检模式*/

    /**cut border*/
    bool is_first_enter_cut_border_last_qr_detection_time_ = true;
    std::chrono::steady_clock::time_point last_qr_cut_border_detection_time_; // 上次检测到充电桩二维码的时间
    bool is_first_cut_border_mode_end_ = true;
    /**cut border*/

    /*******************************************************算法参数********************************************************************/
    // 信标调度 - 出桩

    bool is_unstaking_ = false;
    bool is_enable_unstake_mode_{true}; // 是否开启出桩模式 /*param*/
    float unstake_distance_{1.0};       // 出桩距离 /*param*/
    float unstake_adjust_yaw_{0.52};    // 0.52（30） 出桩调整角度 /*param*/
    float unstake_vel_linear_{0.2};     // 出桩线速度 /*param*/
    float unstake_vel_angular_{0.5};    // 出桩角速度 /*param*/
    float charge_station_width_{0.4};   // 充电桩宽度 /*param*/
    float charge_station_longth_{0.5};  // 充电桩长度 /*param*/
    float angle_proportion_{0.25};      // 旋转角度比例 /*param*/
    bool save_qr_data_{false};
    std::vector<float> qr_detect_x_;
    std::vector<float> qr_detect_y_;
    std::vector<float> qr_detect_yaw_;
    uint64_t stay_all_time_{1000};
    uint64_t stay_time_{50};
    int save_data_num_{3}; // 保存数据帧数

    float mower_linear_{0.2};  /*param*/
    float mower_angular_{0.5}; /*param*/
    // int perception_drive_cooldown_time_{5};   // 5s 感知驱动冷却时间  /*param*/
    int edge_mode_direction_{-1};             // 默认逆时针 -1 /*param*/
    float cross_region_adjust_yaw_{1.57};     // 跨区域后调整方位角 /*param*/
    float cross_region_adjust_displace_{0.3}; // 跨区域后调整位移 /*param*/
    float mark_distance_threshold_{0.5};      // 0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    float camera_2_center_dis_{0.37};         // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/

    // 新增参数
    int edge_perception_drive_cooldown_time_threshold_{10}; // 10s 沿边感知驱动冷却时间  /*param*/
    int qr_detection_cooldown_time_threshold_{30};          // 60s 沿边感知驱动冷却时间  /*param*/
    int mark_detection_cooldown_time_threshold_{30};        // 60s 沿边感知驱动冷却时间  /*param*/

    // 区域探索
    float recharge_distance_threshold_{1.2}; // 区域探索回充距离阈值

    // 割草前处理
    float mower_start_qr_distance_threshold_{0.5}; // 割草前与二维码的距离阈值/*param*/

private:
    // 打滑检测
    std::chrono::steady_clock::time_point last_zero_velocity_time_; // 记录速度接近0的开始时间
    bool is_velocity_zero_ = false;                                 // 标记当前速度是否接近0
    float linear_velocity_threshold_ = 0.02f;                       // 速度阈值 (m/s 或 rad/s) /*param*/
    float angular_velocity_threshold_ = 0.02f;                      // 速度阈值 (m/s 或 rad/s) /*param*/
    static constexpr int ZERO_VELOCITY_THRESHOLD_SECONDS{1};        // 持续时间阈值 (1秒)

    // 添加成员变量
    std::atomic_bool is_recovery_active_{false}; // 标记是否有恢复操作正在进行
    std::mutex recovery_mutex_;
    std::condition_variable recovery_cv_;
    std::future<void> unstake_future_; // 异步任务的 future

private:
    uint64_t last_imu_timestamp_{0};         // 上次接收 IMU 数据的时间戳
    uint64_t motion_detection_timestamp_{0}; // 上次接收运动检测数据的时间戳

    /*******************************************************算法参数********************************************************************/
};

} // namespace fescue_iox

#endif
