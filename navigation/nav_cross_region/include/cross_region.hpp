#ifndef NAVIGATION_CROSS_REGION_ALG_HPP
#define NAVIGATION_CROSS_REGION_ALG_HPP

#include "data_type.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "opencv2/opencv.hpp"
#include "path_track.hpp"
#include "predict_trajectory.hpp"
#include "velocity_publisher.hpp"
#include "velocity_smooth.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{
struct CrossRegionAlgParam
{
    // 多区域通道参数
    float cross_region_linear{0.2};             /*param*/
    float cross_region_angular{0.5};            /*param*/
    float max_distance_threshold{0.95};         /*param*/
    float min_distance_threshold{0.65};         /*param*/
    float cross_region_special_linear{0.2};     // 特殊情况下的线速度（例如感知驱动）/*param*/
    float cross_region_special_angular{0.3};    // 特殊情况下的角速度（例如感知驱动）/*param*/
    float dis_tolerance{0.0};                   // 调整的距离阈值，防止出现冗余旋转 /*param*/
    float cross_region_angle_compensation{0.0}; // 跨区域角度补偿 /*param*/

    float channel_stop_pose_x{-0.5};               /*param*/
    int grass_count_threshold{7};                  /*param*/
    int edge_mode_direction{-1};                   // 默认逆时针 -1/*param*/
    float channel_width{1.5};                      // 通道宽度/*param*/
    float camera_2_center_dis{0.37};               // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/
    float adjust_mode_x_direction_threshold{-0.3}; // 过通道前，矫正模式下x方向阈值 默认-0.5/*param*/

    // 新增参数
    float mark_distance_threshold{0.5};               // 0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    int perception_drive_cooldown_time_threshold{20}; // 20s 感知驱动冷却时间  /*param*/
    float cross_region_adjust_displace{0.7};          // 0.7 跨区域后调整位移 /*param*/
};

enum class CrossRegionStatus : int
{
    InProgress = 0,
    Successed = 1,
    Failed = 2
};

/**
 * @brief 枚举当前区域状态
 */
enum class RegionState : int
{
    IN_GRASS = 0, // 当前处于草地区域
    CROSSING = 1  // 正在跨区域，即已离开草地区域
};

struct CrossRegionAlgResult
{
    bool cross_region_completed{false};
    CrossRegionStatus cross_region_status{CrossRegionStatus::InProgress};
    CrossRegionAlgResult() = default;

    CrossRegionAlgResult(bool cross_region_completed)
        : cross_region_completed(cross_region_completed)
    {
    }

    CrossRegionAlgResult(bool cross_region_completed, CrossRegionStatus status)
        : cross_region_completed(cross_region_completed)
        , cross_region_status(status)
    {
    }
};

class NavigationCrossRegionAlg
{
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationCrossRegionAlg(const CrossRegionAlgParam &param);
    ~NavigationCrossRegionAlg();
    void DataConversion(MarkLocationResult &mark_loc_result);
    void ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result);
    CrossRegionAlgResult DoCrossRegion(PerceptionFusionResult &fusion_result, MarkLocationResult &mark_loc_result);
    void SetCrossRegionAlgParam(const CrossRegionAlgParam &param);
    void GetCrossRegionAlgParam(CrossRegionAlgParam &param);
    void SetMarkLocationResult(const MarkLocationResult &mark_location_result);
    void SetMarkLocationMarkIdCallback(std::function<bool(int)> callback);
    void ProhibitVelPublisher();
    void ResetCrossRegionFlags();
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetAlgoRunningState(MowerRunningState state);
    const char *GetVersion();

private:
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

private:
    void DealFeatureSelect(ThreadControl control, bool state);
    void EdgeFollowDisable();
    void EdgeFollowEnable();
    void UpdateCrossRegionRunningState(CrossRegionRunningState state);
    void FindBeaconsPhase_1(const MarkLocationResult &mark_loc_result);
    bool ProcessPassageRotate(int detect_status);
    void GetPrecisePositionOfBeacon(MarkLocationResult &mark_loc_result, float &x_first, float &y_first,
                                    float &yaw_first, bool &is_get_precise_position);
    void CorrectionOutsideConfidence(const MarkLocationResult &mark_loc_result);
    void ClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular);
    void ControlLinearMotion(const float &pass_point, const float &location, const float &vel_linear, const int &reverse);
    void CounterClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first);
    bool Nongrass2Grass(const PerceptionFusionResult &fusion_result);
    bool CrossRegionFinished(const PerceptionFusionResult &fusion_result);
    void CounterClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void ClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result);
    void DealCrossRegionPhase_1(MarkLocationResult &mark_loc_result);
    void DealCrossRegionPhase_2(MarkLocationResult &mark_loc_result, float &x_first, float &y_first, float &yaw_first);
    void DealCrossRegionPhase_3(MarkLocationResult &mark_loc_result, float &x_first, float &y_first, float &yaw_first);
    CrossRegionAlgResult DealCrossRegionPhase_4(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result);

    // 新增
    void HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active);
    void HandleCrossRegionPerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active);
    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);
    bool SetMarkLocationMarkId(int mark_id);
    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time);
    void HandleCrossRegionCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                            std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time);
    void ResetAndActivateCooldown();
    void DealStage4EnteringPassage(bool &is_walking_before_crossing_passage, bool &cross_region_completed);
    void DealCrossRegionPhase_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                 bool &cross_region_completed);
    void DealCrossRegionPhase_42(MarkLocationResult &mark_loc_result, float &x_first, float &y_first, float &yaw_first);
    void DealCrossRegionPhase_43(MarkLocationResult &mark_loc_result, float &x_first, float &y_first, float &yaw_first);
    void DealCrossRegionPhase_44(MarkLocationResult &mark_loc_result,
                                 const PerceptionFusionResult &fusion_result, bool &cross_region_completed, float &x_first);
    void FindBeaconsPhase_41(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                             bool &cross_region_completed);

    // 0225 新增
    void ComputeB(float A_x, float A_y, float yaw, float B_y, float &B_x, float &distance);
    void ControlProcessToPassPoints(const float &x_first, const float &y_first, const float &yaw_first);
    void ControlProcessToEndPoints(const float &x_first, const float &y_first, const float &yaw_first);
    int PairNumber(int n);
    void SafeLinearMotion(float target_dis, float current_dis,
                          float vel_linear, int reverse,
                          MarkLocationResult &mark_loc_result);
    void CheckFrontBeaconCollision(const MarkLocationResult &mark_loc_result);

private:
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void InitPublisher();

private:
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;
    std::function<void(CrossRegionRunningState)> cross_region_running_state_callback_;
    std::function<bool(int)> set_mark_id_callback_;

    // 运行状态量
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-未开始，1-运行中，2-暂停

    bool phase_1_completed_ = false; //! 第一阶段。开启沿边，直到检测到信标二维码才关闭沿边
    bool phase_2_completed_ = false; //! 第二阶段。根据定位给的置信度区间，找到置信度高的区域。然后驱使接近信标，直到在阈值范围内得到精确的二维码解算位姿
    bool phase_3_completed_ = false; //! 第三阶段。获取到精确的位姿后，调整好位姿

    bool phase_41_completed_ = false;
    bool phase_42_completed_ = false;
    bool phase_43_completed_ = false;

    std::deque<GrassDetectStatus> frames_;
    bool is_walking_before_crossing_passage_{false};

    // 判断是否从草地到非草地
    uint64_t passage_rotate_time_{0};
    uint64_t passage_rotate_begin_time_{0};

    // 算法运行时变量
    bool is_cooldown_active_ = false;                                       // 用于控制冷却机制是否激活
    std::chrono::time_point<std::chrono::steady_clock> last_cooldown_time_; // 用于记录冷却开始时间
    std::chrono::seconds perception_drive_duration_{0};
    RegionState current_state_{RegionState::IN_GRASS};
    int next_paired_beacon_id_ = -1;      // 下一对信标id
    bool non_grass_area_reached_ = false; // 第四阶段，新增状态标志
    bool emergency_stop_ = false;         // 紧急停止标志
    bool front_beacon_detected_ = false;  // 安全移动控制函数 前方信标是否检测到
    bool is_first_non_grass_area_reached_ = true;

    /*******************************************************算法参数********************************************************************/
    // 多区域通道参数
    float cross_region_linear_{0.2};             /*param*/
    float cross_region_angular_{0.3};            /*param*/
    float max_distance_threshold_{0.95};         /*param*/
    float min_distance_threshold_{0.65};         /*param*/
    float cross_region_special_linear_{0.2};     // 特殊情况下的线速度（例如感知驱动）/*param*/
    float cross_region_special_angular_{0.3};    // 特殊情况下的角速度（例如感知驱动）/*param*/
    float dis_tolerance_{0.0};                   // 调整的距离阈值，防止出现冗余旋转 /*param*/
    float cross_region_angle_compensation_{0.0}; // 跨区域角度补偿 /*param*/

    float channel_stop_pose_x_{-0.5};               /*param*/
    int grass_count_threshold_{7};                  /*param*/
    int edge_mode_direction_{-1};                   // 默认逆时针 -1/*param*/
    float channel_width_{1.5};                      // 通道宽度/*param*/
    float camera_2_center_dis_{0.37};               // 小车摄像头到旋转中心的距离为切尔西(0.37) 格力博(0.45) /*param*/
    float adjust_mode_x_direction_threshold_{-0.3}; // 过通道前，矫正模式下x方向阈值 默认-0.5/*param*/

    // 新增参数
    float mark_distance_threshold_{0.5};               // 0.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    int perception_drive_cooldown_time_threshold_{20}; // 5s 感知驱动冷却时间  /*param*/
    float cross_region_adjust_displace_{0.7};          // 跨区域后调整位移 /*param*/

    /*******************************************************算法参数********************************************************************/

    // 信标检测变量
    CrossRegionRunningState cross_region_state_{CrossRegionRunningState::UNDEFINED};
    std::mutex cross_region_mutex_;

    // 速度发布
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};

    bool apply_coordinate_transform_{true};
    std::mutex mark_loc_mutex_;
    MarkLocationResult mark_loc_result_;
    MarkLocationResult mark_loc_result_law_;

private:
    std::chrono::steady_clock::time_point straight_start_time_; // 直线运动开始时间
    bool is_straight_timing_started_ = false;                   // 标志是否开始计时
    static constexpr int CROSS_AREA_WALKING_TIMEOUT{40};
    std::unique_ptr<iox_exception_publisher> pub_exception_;

private:
    std::chrono::steady_clock::time_point beacon_pairing_error_start_time_;
    bool beacon_pairing_error_active_{false};
};

} // namespace fescue_iox

#endif
