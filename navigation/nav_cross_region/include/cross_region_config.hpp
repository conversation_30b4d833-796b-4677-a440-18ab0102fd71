#pragma once

#include "utils/config.hpp"

#include <string>

namespace fescue_iox
{

struct NavigationCrossRegionAlgConfig
{
    // 多区域通道参数
    float cross_region_linear{0.2};             /*param*/
    float cross_region_angular{0.5};            /*param*/
    float max_distance_threshold{0.95};         /*param*/
    float min_distance_threshold{0.65};         /*param*/
    float cross_region_special_linear{0.2};     // 特殊情况下的线速度（例如感知驱动）/*param*/
    float cross_region_special_angular{0.3};    // 特殊情况下的角速度（例如感知驱动）/*param*/
    float dis_tolerance{0.0};                   // 调整的距离阈值，防止出现冗余旋转 /*param*/
    float cross_region_angle_compensation{0.0}; // 跨区域角度补偿 /*param*/

    float channel_stop_pose_x{-0.5};               /*param*/
    int grass_count_threshold{7};                  /*param*/
    int edge_mode_direction{-1};                   // 默认逆时针 -1/*param*/
    float channel_width{1.5};                      // 通道宽度/*param*/
    float camera_2_center_dis{0.37};               // 小车摄像头到旋转中心的距离为0.45/*param*/
    float adjust_mode_x_direction_threshold{-0.3}; // 过通道前，矫正模式下x方向阈值 默认-0.5/*param*/

    // 新增参数
    float mark_distance_threshold{0.5};               // 1.5 信标相对小车摄像头的距离阈值，判断是否在区域范围内 /*param*/
    int perception_drive_cooldown_time_threshold{20}; // 20s 感知驱动冷却时间  /*param*/

    float cross_region_adjust_displace{0.7}; // 0.7 跨区域后调整位移 /*param*/
    NavigationCrossRegionAlgConfig() = default;
    ~NavigationCrossRegionAlgConfig() = default;
    NavigationCrossRegionAlgConfig(const NavigationCrossRegionAlgConfig &config) = default;
    NavigationCrossRegionAlgConfig &operator=(const NavigationCrossRegionAlgConfig &config);
    std::string toString() const;
};

bool operator==(const NavigationCrossRegionAlgConfig &lhs, const NavigationCrossRegionAlgConfig &rhs);
bool operator!=(const NavigationCrossRegionAlgConfig &lhs, const NavigationCrossRegionAlgConfig &rhs);

} // namespace fescue_iox
