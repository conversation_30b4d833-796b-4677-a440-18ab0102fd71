#ifndef NAVIGATION_CROSS_REGION_NODE_HPP
#define NAVIGATION_CROSS_REGION_NODE_HPP

#include "cross_region.hpp"
#include "cross_region_config.hpp"
#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "iceoryx_hoofs/cxx/deadline_timer.hpp"
#include "iceoryx_hoofs/cxx/optional.hpp"
#include "iceoryx_hoofs/cxx/stack.hpp"
#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/variant.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"
#include "iceoryx_posh/popo/client.hpp"
#include "iceoryx_posh/popo/listener.hpp"
#include "iceoryx_posh/popo/publisher.hpp"
#include "iceoryx_posh/popo/server.hpp"
#include "iceoryx_posh/popo/subscriber.hpp"
#include "iceoryx_posh/popo/user_trigger.hpp"
#include "iceoryx_posh/popo/wait_set.hpp"
#include "iceoryx_posh/runtime/posh_runtime.hpp"
#include "iox/signal_watcher.hpp"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_cross_region_final_result__struct.h"
#include "ob_mower_msgs/nav_cross_region_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_srvs/mark_location_detect_mark_id_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_alg_param_service__struct.h"
#include "ob_mower_srvs/nav_cross_region_node_param_service__struct.h"
#include "opencv2/opencv.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_client_mower.hpp"
#include "utils/iceoryx_publisher_mower.hpp"
#include "utils/iceoryx_server_mower.hpp"
#include "utils/iceoryx_subscriber_mower.hpp"

#include <memory>
#include <mutex>
#include <thread>

namespace fescue_iox
{

class NavigationCrossRegionNode
{
    using iox_cross_region_final_result_publisher = iox::popo::Publisher<fescue_msgs__msg__NavCrossRegionFinalResult>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using iox_cross_region_state_publisher = iox::popo::Publisher<fescue_msgs__msg__CrossRegionStateData>;

    using get_node_param_request = fescue_msgs__srv__GetNavigationCrossRegionNodeParam_Request;
    using get_node_param_response = fescue_msgs__srv__GetNavigationCrossRegionNodeParam_Response;
    using set_node_param_request = fescue_msgs__srv__SetNavigationCrossRegionNodeParam_Request;
    using set_node_param_response = fescue_msgs__srv__SetNavigationCrossRegionNodeParam_Response;

    using get_alg_param_request = fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Request;
    using get_alg_param_response = fescue_msgs__srv__GetNavigationCrossRegionAlgParam_Response;
    using set_alg_param_request = fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Request;
    using set_alg_param_response = fescue_msgs__srv__SetNavigationCrossRegionAlgParam_Response;

public:
    NavigationCrossRegionNode(const std::string &node_name);
    ~NavigationCrossRegionNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitAlgorithmParam();
    void InitAlgorithm();
    void DeinitAlgorithm();
    void InitLogger();
    void InitSubscriber();
    void InitPublisher();
    void InitService();
    void CrossRegionThread();
    void InitHeartbeat();

private:
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    bool GetCrossRegionNodeParam(fescue_msgs__msg__NavigationCrossRegionNodeParam &data);
    bool SetCrossRegionNodeParam(const fescue_msgs__msg__NavigationCrossRegionNodeParam &data);
    bool GetCrossRegionAlgParam(fescue_msgs__msg__NavigationCrossRegionAlgParam &data);
    bool SetCrossRegionAlgParam(const fescue_msgs__msg__NavigationCrossRegionAlgParam &data);
    void ConfigParamToAlgParam(const NavigationCrossRegionAlgConfig &config, CrossRegionAlgParam &param);
    void AlgParamToConfigParam(NavigationCrossRegionAlgConfig &config, const CrossRegionAlgParam &param);
    void PublishCrossRegionFinalResult(const CrossRegionAlgResult &result);
    bool DealMarkLocationMarkIdCallback(int mark_id);
    void DealFeatureSelectCallback(const std::vector<FeatureSelectData> &data);
    void DealCrossRegionRunningStateCallback(CrossRegionRunningState state);
    void SetCrossRegionVelPublisherProhibit(bool prohibit)
    {
        if (cross_region_alg_)
        {
            cross_region_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

private:
    // 订阅
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>> sub_mark_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>> sub_cross_region_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};

    // 发布
    std::unique_ptr<iox_nav_alg_ctrl_publisher> pub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<iox_cross_region_state_publisher> pub_cross_region_state_{nullptr};
    std::unique_ptr<iox_cross_region_final_result_publisher> pub_cross_region_final_result_{nullptr}; // 发布跨区域最终执行结果

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<get_alg_param_request, get_alg_param_response>> service_get_alg_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_alg_param_request, set_alg_param_response>> service_set_alg_param_{nullptr};

    std::atomic_bool cross_region_enable_{false};

private:
    std::mutex mark_loc_mutex_;
    std::mutex fusion_mutex_;
    MarkLocationResult mark_loc_result_;
    PerceptionFusionResult fusion_result_;
    std::mutex cross_region_mtx_;
    CrossRegionRunningState cross_region_state_{CrossRegionRunningState::UNDEFINED};

    std::thread cross_region_thread_;
    std::atomic_bool thread_running_{true};

    CrossRegionAlgParam cross_region_param_;
    std::unique_ptr<NavigationCrossRegionAlg> cross_region_alg_{nullptr};

    // Parameters
    std::string node_name_{"navigation_cross_region_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string cross_region_alg_conf_file_{"conf/navigation_cross_region_node/cross_region.yaml"};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
};

} // namespace fescue_iox

#endif
