#include "cross_region.hpp"

#include "cross_region_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationCrossRegionAlg::NavigationCrossRegionAlg(const CrossRegionAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("CrossRegion"))
{
    last_cooldown_time_ = std::chrono::steady_clock::now();
    straight_start_time_ = std::chrono::steady_clock::now();
    beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();

    SetCrossRegionAlgParam(param);
    InitPublisher();
}

NavigationCrossRegionAlg::~NavigationCrossRegionAlg()
{
    PublishVelocity(0.0, 0.0, 1000); // 持续发送1s
    LOG_WARN("NavigationCrossRegionAlg exit!");
}

void NavigationCrossRegionAlg::DataConversion(MarkLocationResult &mark_loc_result)
{
    LOG_INFO("[CrossRegion]  坐标转换前 MarkLocation result: detect_status: {} mark_perception_status: {} mark_perception_direction: {} roi_confidence: {} "
             "target_direction : {} markID : {} v_markID_dis.size : {} xyz({} {} {}) yaw({})",
             mark_loc_result.detect_status, mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
             mark_loc_result.roi_confidence, mark_loc_result.target_direction, mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
             mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
             Radians2Degrees(mark_loc_result.xyzrpw.w));

    for (const auto &mark_id_distance : mark_loc_result.mark_id_distance)
    {
        LOG_INFO("[CrossRegion] mark_id_distance 信标和距离: mark_id({}), distance({})",
                 mark_id_distance.mark_id, mark_id_distance.distance);
    }

    if (mark_loc_result.roi_confidence >= 60 && mark_loc_result.roi_confidence <= 100) // 60~100
    {
        mark_loc_result.roi_confidence = 1; // 1 表示在ROI区域
    }
    else if (mark_loc_result.roi_confidence < 60 && mark_loc_result.roi_confidence >= 0) // 0~60
    {
        mark_loc_result.roi_confidence = 0; // 0 表示未在ROI区域
    }
    else
    {
        mark_loc_result.roi_confidence = -1; //-1 表示未检测成功
    }

    // 将以mark为右手坐标系，camera相对于mark的坐标转化为 ————》base_link相对于mark的坐标
    if (mark_loc_result.detect_status == 2)
    {
        // 输入camera相对于base_link的固定坐标
        Pose_Mark camera_to_base_link = {camera_2_center_dis_, 0.0, 0.0, 0.0, 0.0, 0.0};

        // 输入camera相对于mark的坐标
        Pose_Mark camera_to_mark = {mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                                    mark_loc_result.xyzrpw.r, mark_loc_result.xyzrpw.p, mark_loc_result.xyzrpw.w};

        // 计算base_link相对于mark的坐标
        Pose_Mark base_link_to_mark = calculateBaseLinkRelativeToMark(camera_to_mark, camera_to_base_link);

        mark_loc_result.xyzrpw.x = base_link_to_mark.x;
        mark_loc_result.xyzrpw.y = base_link_to_mark.y;
        mark_loc_result.xyzrpw.z = base_link_to_mark.z;
        mark_loc_result.xyzrpw.r = base_link_to_mark.roll;
        mark_loc_result.xyzrpw.p = base_link_to_mark.pitch;
        mark_loc_result.xyzrpw.w = base_link_to_mark.yaw;

        LOG_INFO("[CrossRegion]  坐标转换后 MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
                 "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
                 mark_loc_result.detect_status,
                 mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                 mark_loc_result.roi_confidence, mark_loc_result.target_direction,
                 mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                 mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                 Radians2Degrees(mark_loc_result.xyzrpw.w));
    }
}

void NavigationCrossRegionAlg::SetCrossRegionAlgParam(const CrossRegionAlgParam &param)
{
    cross_region_linear_ = param.cross_region_linear;
    cross_region_angular_ = param.cross_region_angular;
    max_distance_threshold_ = param.max_distance_threshold;
    min_distance_threshold_ = param.min_distance_threshold;
    cross_region_special_linear_ = param.cross_region_special_linear;
    cross_region_special_angular_ = param.cross_region_special_angular;
    dis_tolerance_ = param.dis_tolerance;
    cross_region_angle_compensation_ = param.cross_region_angle_compensation;
    channel_stop_pose_x_ = param.channel_stop_pose_x;
    grass_count_threshold_ = param.grass_count_threshold;
    edge_mode_direction_ = param.edge_mode_direction;
    channel_width_ = param.channel_width;
    camera_2_center_dis_ = param.camera_2_center_dis;
    adjust_mode_x_direction_threshold_ = param.adjust_mode_x_direction_threshold;
    mark_distance_threshold_ = param.mark_distance_threshold;
    perception_drive_cooldown_time_threshold_ = param.perception_drive_cooldown_time_threshold;
    cross_region_adjust_displace_ = param.cross_region_adjust_displace;
}

void NavigationCrossRegionAlg::GetCrossRegionAlgParam(CrossRegionAlgParam &param)
{
    param.cross_region_linear = cross_region_linear_;
    param.cross_region_angular = cross_region_angular_;
    param.max_distance_threshold = max_distance_threshold_;
    param.min_distance_threshold = min_distance_threshold_;
    param.cross_region_special_linear = cross_region_special_linear_;
    param.cross_region_special_angular = cross_region_special_angular_;
    param.dis_tolerance = dis_tolerance_;
    param.cross_region_angle_compensation = cross_region_angle_compensation_;
    param.channel_stop_pose_x = channel_stop_pose_x_;
    param.grass_count_threshold = grass_count_threshold_;
    param.edge_mode_direction = edge_mode_direction_;
    param.channel_width = channel_width_;
    param.camera_2_center_dis = camera_2_center_dis_;
    param.adjust_mode_x_direction_threshold = adjust_mode_x_direction_threshold_;
    param.mark_distance_threshold = mark_distance_threshold_;
    param.perception_drive_cooldown_time_threshold = perception_drive_cooldown_time_threshold_;
    param.cross_region_adjust_displace = cross_region_adjust_displace_;
}

void NavigationCrossRegionAlg::SetMarkLocationResult(const MarkLocationResult &mark_loc_result)
{
    if (apply_coordinate_transform_)
    {
        std::lock_guard<std::mutex> lck(mark_loc_mutex_);
        mark_loc_result_ = mark_loc_result;
        mark_loc_result_law_ = mark_loc_result;
        DataConversion(mark_loc_result_);
    }
}

void NavigationCrossRegionAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationCrossRegionAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationCrossRegionAlg] Unknown state {}!", static_cast<int>(state));
    }
}

const char *NavigationCrossRegionAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationCrossRegionAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationCrossRegionAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationCrossRegionAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationCrossRegionAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationCrossRegionAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationCrossRegionAlg::ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result)
{
    // 打印时间差
    LOG_WARN_THROTTLE(2000, "[CrossRegion] 沿边感知驱动冷却的计时（秒）：({})!", perception_drive_duration_.count());
}

CrossRegionAlgResult NavigationCrossRegionAlg::DoCrossRegion(PerceptionFusionResult &fusion_result,
                                                             MarkLocationResult &mark_loc_result)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "[CrossRegion] DoCrossRegion() is PAUSE!");
        return CrossRegionAlgResult(false, CrossRegionStatus::InProgress);
    }

    // 感知驱动冷却时间
    ShowMowerRunningInfo(mark_loc_result);

    float x_first = 0.0;
    float y_first = 0.0;
    float yaw_first = 0.0;

    DealCrossRegionPhase_1(mark_loc_result);
    DealCrossRegionPhase_2(mark_loc_result, x_first, y_first, yaw_first);
    DealCrossRegionPhase_3(mark_loc_result, x_first, y_first, yaw_first);

    return DealCrossRegionPhase_4(mark_loc_result, fusion_result);
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_1(MarkLocationResult &mark_loc_result)
{
    if (!phase_1_completed_ && !phase_2_completed_ && !phase_3_completed_)
    {
        FindBeaconsPhase_1(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_2(MarkLocationResult &mark_loc_result,
                                                      float &x_first, float &y_first, float &yaw_first)
{
    if (phase_1_completed_ && !phase_2_completed_ && !phase_3_completed_)
    {
        EdgeFollowDisable();

        if (mark_loc_result.detect_status != 2) // 信标不能计算出位姿
        {
            LOG_INFO("[CrossRegion] [第二阶段] 信标不能计算出位姿，开启第一阶段，沿边找信标");
            phase_1_completed_ = false;
            PublishZeroVelocity();
        }
        else
        {
            LOG_INFO("[CrossRegion] [第二阶段] 获取精确的信标二维码位姿");
            GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_2_completed_);

            // // 在置信区域内
            // if (mark_loc_result.roi_confidence == 1)
            // {
            //     LOG_INFO("[CrossRegion] [第二阶段] 信标出现在置信区域内");
            //     GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_2_completed_);
            // }
            // else // 在置信区域外
            // {
            //     LOG_INFO("[CrossRegion] [第二阶段] 信标出现在置信区域外");
            //     CorrectionOutsideConfidence(mark_loc_result);
            // }
        }
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_3(MarkLocationResult &mark_loc_result,
                                                      float &x_first, float &y_first, float &yaw_first)
{
    if (phase_1_completed_ && phase_2_completed_ && !phase_3_completed_)
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS; // 第三阶段，定位检测到二维码，可以算出位姿
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS);
        }

        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_INFO("[CrossRegion] [第三阶段] 不考虑顺时针沿边跨区域的情况");
            // LOG_INFO("[CrossRegion] [第三阶段] 沿边顺时针的情况下。根据信标调整到pass_point");
            // ClockwiseAlignPassPoints(x_first, y_first, yaw_first);
        }
        else // 沿边逆时针
        {
            LOG_INFO("[CrossRegion] [第三阶段] 沿边逆时针的情况下。根据信标调整到pass_point");
            // CounterClockwiseAlignPassPoints(x_first, y_first, yaw_first);
            ControlProcessToPassPoints(x_first, y_first, yaw_first);
        }

        LOG_INFO("[CrossRegion] [第三阶段] 第三阶段完成。开始沿通道行走");
        phase_3_completed_ = true; // 允许通过通道
    }
}

CrossRegionAlgResult NavigationCrossRegionAlg::DealCrossRegionPhase_4(MarkLocationResult &mark_loc_result,
                                                                      PerceptionFusionResult &fusion_result)
{
    bool cross_region_completed = false;

    if (phase_1_completed_ && phase_2_completed_ && phase_3_completed_)
    {
        if (!is_walking_before_crossing_passage_) // 判断过通道前是否有直行
        {
            DealStage4EnteringPassage(is_walking_before_crossing_passage_, cross_region_completed);
            return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::InProgress);
        }
        else
        {
            LOG_INFO("[CrossRegion] [第四阶段] 已直线一定距离，开始检测信标");

            //  1. 获取精确的信标二维码位姿
            float x_first = 0.0;
            float y_first = 0.0;
            float yaw_first = 0.0;
            DealCrossRegionPhase_41(mark_loc_result, fusion_result, cross_region_completed);
            DealCrossRegionPhase_42(mark_loc_result, x_first, y_first, yaw_first);
            DealCrossRegionPhase_43(mark_loc_result, x_first, y_first, yaw_first);
            DealCrossRegionPhase_44(mark_loc_result, fusion_result, cross_region_completed, x_first);
        }
    }

    return CrossRegionAlgResult(cross_region_completed, CrossRegionStatus::Successed);
}

void NavigationCrossRegionAlg::DealStage4EnteringPassage(bool &is_walking_before_crossing_passage, bool &cross_region_completed)
{
    LOG_INFO("[CrossRegion] [第四阶段] 过通道前有直行1s!");
    PublishVelocity(cross_region_linear_, 0.0, 1000);

    is_walking_before_crossing_passage = true;

    // 第四阶段，刚入通道，且已直行一定距离
    {
        std::lock_guard<std::mutex> lck(cross_region_mutex_);
        cross_region_state_ = CrossRegionRunningState::STAGE4_ENTERING_CHANNEL; // 第四阶段，刚入通道，且已直行一定距离
        UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_ENTERING_CHANNEL);
    }

    cross_region_completed = false;
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_41(MarkLocationResult &mark_loc_result, PerceptionFusionResult &fusion_result,
                                                       bool &cross_region_completed)
{
    if (!phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
    {
        FindBeaconsPhase_41(mark_loc_result, fusion_result, cross_region_completed);
    }

    // 仅在非草地或草地检测阶段的直线运动时开始计时
    if (non_grass_area_reached_)
    {
        if (!is_straight_timing_started_)
        {
            straight_start_time_ = std::chrono::steady_clock::now();
            is_straight_timing_started_ = true;
            LOG_WARN("[CrossRegion] [第四阶段] 直线计时开始");
        }

        // 计算经过时间
        auto current_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - straight_start_time_);
        LOG_WARN("[CrossRegion] [第四阶段] 直线运动持续时间: {} 秒", duration.count());

        // 检查直线运动是否超过CROSS_AREA_WALKING_TIMEOUT秒
        if (duration.count() >= CROSS_AREA_WALKING_TIMEOUT) // 40s-》8m
        {
            LOG_ERROR("[CrossRegion] 直行时间超过 {} 秒，上报异常", CROSS_AREA_WALKING_TIMEOUT);
            PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_CROSS_REGION_GRASS_TIMEOUT_EXCEPTION);
        }
    }
    else
    {
        // 当不在直线运动时重置计时标志
        LOG_WARN("[CrossRegion] [第四阶段] 计时重新开始");
        is_straight_timing_started_ = false;
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_42(MarkLocationResult &mark_loc_result,
                                                       float &x_first, float &y_first, float &yaw_first)
{
    if (phase_41_completed_ && !phase_42_completed_ && !phase_43_completed_)
    {
        if (mark_loc_result.detect_status != 2) // 信标不能计算出位姿
        {
            LOG_INFO("[CrossRegion] [第四阶段] 信标不能计算出位姿，开启第一阶段，直线跨区域");
            phase_41_completed_ = false;
            PublishZeroVelocity();

            // 刚入通道没有检测到信标
            if (mark_loc_result.detect_status == 0)
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON; // 第四阶段，定位未检测到二维码
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON);
            }
            else if (mark_loc_result.detect_status == 1)
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS; // 第四阶段，定位检测到二维码，不能算出位姿
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS);
            }
        }
        else
        {
            LOG_INFO("[CrossRegion] [第四阶段] 定位能计算出位姿");
            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS;
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS);
            }

            LOG_INFO("[CrossRegion] [第四阶段] 获取精确的信标二维码位姿");

            //  1. 获取精确的信标二维码位姿
            x_first = 0.0;
            y_first = 0.0;
            yaw_first = 0.0;
            GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_42_completed_);

            // // 在置信区域内
            // if (mark_loc_result.roi_confidence == 1)
            // {
            //     LOG_INFO("[CrossRegion] [第四阶段] 信标出现在置信区域内");

            //     //  1. 获取精确的信标二维码位姿
            //     x_first = 0.0;
            //     y_first = 0.0;
            //     yaw_first = 0.0;
            //     GetPrecisePositionOfBeacon(mark_loc_result, x_first, y_first, yaw_first, phase_42_completed_);
            // }
            // else // 在置信区域外
            // {
            //     LOG_INFO("[CrossRegion] [第四阶段] 信标出现在置信区域外");
            //     CorrectionOutsideConfidence(mark_loc_result); // 信标在置信度范围外，对小车进行转向矫正
            // }
        }
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_43(MarkLocationResult &mark_loc_result,
                                                       float &x_first, float &y_first, float &yaw_first)
{
    if (phase_41_completed_ && phase_42_completed_ && !phase_43_completed_)
    {
        LOG_INFO("[CrossRegion] [第四阶段] 已完成获取精确位姿 x_first = {}, y_first = {}, yaw_first = {}",
                 x_first, y_first, Radians2Degrees(yaw_first));

        // 根据获取到的位姿，矫正小车的位姿到截止点，且设置停止距离
        if (edge_mode_direction_ == 1) // 沿边顺时针
        {
            LOG_INFO("[CrossRegion] [第四阶段] 不考虑顺时针沿边跨区域的情况");

            // LOG_INFO("[CrossRegion] [第四阶段] 沿边顺时针的情况下。根据信标调整到end_point");
            // ClockwiseAlignEndPoints(x_first, y_first, yaw_first);
        }
        else // 沿边逆时针
        {
            LOG_INFO("[CrossRegion] [第四阶段] 沿边逆时针的情况下。根据信标调整到end_point");
            // CounterClockwiseAlignEndPoints(x_first, y_first, yaw_first);
            ControlProcessToEndPoints(x_first, y_first, yaw_first);
        }

        LOG_INFO("[CrossRegion] [第四阶段] 第三阶段完成。开始调整到终止点附近后，驱动小车驶向目标停止点");
        phase_43_completed_ = true;
    }
}

void NavigationCrossRegionAlg::DealCrossRegionPhase_44(MarkLocationResult &mark_loc_result,
                                                       const PerceptionFusionResult &fusion_result, bool &cross_region_completed, float &x_first)
{
    if (phase_41_completed_ && phase_42_completed_ && phase_43_completed_)
    {
        // 调整到终止点附近后，驱动小车驶向目标停止点 channel_stop_pose_x_= -0.5
        LOG_INFO("[CrossRegion] [第四阶段] 调整到终止点附近后，驱动小车驶向目标停止点");
        ControlLinearMotion(channel_stop_pose_x_, x_first, cross_region_linear_, 1);

        // 到达指定位置后，发送停止信号
        PublishVelocity(0.0, 0.0, 1000); // 持续发送1s
        cross_region_completed = true;
        LOG_INFO("[CrossRegion] [第四阶段] 跨区域算法执行结束");

        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION; // 第四阶段，信标检测退出跨区域
            UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION);
        }
    }
}

void NavigationCrossRegionAlg::ResetCrossRegionFlags()
{
    is_walking_before_crossing_passage_ = false;

    phase_1_completed_ = false;
    phase_2_completed_ = false;
    phase_3_completed_ = false;

    phase_41_completed_ = false;
    phase_42_completed_ = false;
    phase_43_completed_ = false;

    is_cooldown_active_ = false;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    perception_drive_duration_ = std::chrono::seconds(0);

    frames_.clear();
    current_state_ = RegionState::IN_GRASS;
    next_paired_beacon_id_ = -1;     // 下一对信标id
    non_grass_area_reached_ = false; // 第四阶段，新增状态标志
    emergency_stop_ = false;         // 紧急停止标志
    front_beacon_detected_ = false;
    is_first_non_grass_area_reached_ = true;
    // UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);

    // 重置直线计时器
    is_straight_timing_started_ = false;
    straight_start_time_ = std::chrono::steady_clock::now();

    // 重置信标配对错误状态
    beacon_pairing_error_active_ = false;
    beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();
}

void NavigationCrossRegionAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationCrossRegionAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationCrossRegionAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationCrossRegionAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationCrossRegionAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationCrossRegionAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}

/**
 * @brief 第一阶段。沿边找信标
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::FindBeaconsPhase_1(const MarkLocationResult &mark_loc_result)
{
    if (mark_loc_result.mark_perception_status == 0) // 感知没有检测到信标
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::EDGE_FINDING_BEACON; // 新增沿边找信标状态
            UpdateCrossRegionRunningState(CrossRegionRunningState::EDGE_FINDING_BEACON);
        }

        LOG_INFO("[CrossRegion] [第一阶段] 1. 感知没有检测到信标, 开启沿边");
        EdgeFollowEnable(); // 开启沿边
    }
    else // 感知检测到信标
    {
        {
            std::lock_guard<std::mutex> lck(cross_region_mutex_);
            cross_region_state_ = CrossRegionRunningState::PER_FOUND_BEACON; // 感知定位已经找到信标
            UpdateCrossRegionRunningState(CrossRegionRunningState::PER_FOUND_BEACON);
        }

        LOG_INFO("[CrossRegion] [第一阶段] 2. 感知检测到信标");
        if (mark_loc_result.mark_id_distance.size() <= 0) // 定位的mark_id_distance没值
        {
            LOG_INFO("[CrossRegion] [第一阶段] 2.1 定位的mark_id_distance无值");
            HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
        }
        else // 定位的mark_id_distance有值
        {
            LOG_INFO("[CrossRegion] [第一阶段] 2.2 定位的mark_id_distance有值");

            // 判断信标是否有效。（mark_id_distance小于50cm认为当前信标有效）
            int shortest_dis_inx = -1;
            std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
            FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

            if (shortest_dis_inx == -1) // 若信标无效。不操作，继续之前的动作
            {
                LOG_INFO("[CrossRegion] [第一阶段]  2.2.1 跨区信标无效");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // 若信标有效。检测栈容器是否为空
            {
                LOG_INFO("[CrossRegion] [第一阶段]  2.2.2 跨区信标有效");
                LOG_INFO("[CrossRegion] [第一阶段]  2.2.2 有效的信标为 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                // 重置冷却时间戳，并激活冷却机制
                last_cooldown_time_ = std::chrono::steady_clock::now();
                is_cooldown_active_ = true;
                // ResetAndActivateCooldown();

                // 向定位发送信标id以跨区域
                SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);
                next_paired_beacon_id_ = PairNumber(mark_id_distance_vec[shortest_dis_inx].mark_id);
                LOG_INFO("[CrossRegion] [第一阶段]  2.2.2 下一对信标id为 {}", next_paired_beacon_id_);

                if (mark_loc_result.detect_status == 2)
                {
                    LOG_INFO("[CrossRegion] [第一阶段]  2.2.2.1 信标可以计算出位姿");

                    // 执行跨通道线程，关闭沿边线程
                    LOG_INFO("[CrossRegion] [第一阶段]  2.2.2.1 第一阶段完成, 开始跨区域，关闭沿边线程");
                    EdgeFollowDisable();
                    phase_1_completed_ = true;
                }
            }
        }
    }
}

/**
 * @brief 第四阶段。跨区找信标
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::FindBeaconsPhase_41(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                                   bool &cross_region_completed)
{
    // 新增：无论是否到达非草地都持续检测状态变化
    if (CrossRegionFinished(fusion_result))
    {
        LOG_INFO("[CrossRegion] [第四阶段] 检测到草地->非草地->草地状态转换");
        // ControlLinearMotion(cross_region_adjust_displace_, 0.0, cross_region_linear_, 1);

        // 继续直行一定距离，使用安全移动模式
        SafeLinearMotion(cross_region_adjust_displace_, 0.0,
                         cross_region_linear_, 1, mark_loc_result);

        if (!emergency_stop_)
        {
            LOG_INFO("[CrossRegion] [41] 跨区域算法执行结束");
            PublishVelocity(0.0, 0.0, 1000);
            cross_region_completed = true;

            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION; // 第四阶段，非草地到草地退出跨区域
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION);
            }

            return; // 直接返回不再执行后续逻辑
        }
    }

    // ====== 新增前提条件：必须先到达非草地 ======
    if (!non_grass_area_reached_) /**非草地没有到达 */
    {
        if (fusion_result.grass_detecte_status == GrassDetectStatus::NO_GRASS) /**当前在非草地中 */
        {
            non_grass_area_reached_ = true; /**非草地到达 */
            LOG_INFO("[CrossRegion] [第四阶段] 非草地区域已到达，开启通道通行逻辑");

            {
                std::lock_guard<std::mutex> lck(cross_region_mutex_);
                cross_region_state_ = CrossRegionRunningState::STAGE4_NON_GRASS_REACHED; // 第四阶段，非草地到达
                UpdateCrossRegionRunningState(CrossRegionRunningState::STAGE4_NON_GRASS_REACHED);
            }
        }
        else /**当前在草地中 */
        {
            non_grass_area_reached_ = false; /**非草地未到达 */
            PublishVelocity(cross_region_linear_, 0);
            LOG_INFO("[CrossRegion] [第四阶段] 当前未到达非草地状态, grass_detecte_status: {}", static_cast<int>(fusion_result.grass_detecte_status));
        }
    }
    else /**非草地到达 */
    {
        if (is_first_non_grass_area_reached_)
        {
            // 强制直行0.5m。防止识别到第一个信标
            ControlLinearMotion(0.5, 0.0, cross_region_linear_, 1);
            is_first_non_grass_area_reached_ = false;
        }

        // ====== 非草地状态已达到，进入后续判断 ======
        if (mark_loc_result.mark_perception_status == 0) // 感知没有检测到信标
        {
            LOG_INFO("[CrossRegion] [第四阶段] 1. 感知没有检测到信标");

            // 持续直行通过通道
            LOG_INFO("[CrossRegion] [第四阶段] 1. 持续直行通过通道");
            PublishVelocity(cross_region_linear_, 0); // 直走
        }
        else // 感知检测到信标
        {
            LOG_INFO("[CrossRegion] [第四阶段] 2. 感知检测到信标");
            if (mark_loc_result.mark_id_distance.size() <= 0) // 定位的mark_id_distance没值
            {
                LOG_INFO("[CrossRegion] [第四阶段] 2.1 定位的mark_id_distance没值");
                HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // 定位的mark_id_distance有值
            {
                LOG_INFO("[CrossRegion] [第四阶段] 2.2 定位的mark_id_distance有值");

                // 判断信标是否有效。（mark_id_distance小于50cm认为当前信标有效）
                int shortest_dis_inx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

                if (shortest_dis_inx == -1) // 若信标无效。不操作，继续之前的动作
                {
                    LOG_INFO("[CrossRegion] [第四阶段] 2.2.1 跨区信标无效");
                    HandleCrossRegionPerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                }
                else // 若信标有效
                {
                    LOG_INFO("[CrossRegion] 2.2.2 跨区信标有效");
                    LOG_INFO("[CrossRegion] 2.2.2 有效的信标为 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    // 调用函数重置冷却机制
                    // 重置冷却时间戳，并激活冷却机制
                    last_cooldown_time_ = std::chrono::steady_clock::now();
                    is_cooldown_active_ = true;
                    // ResetAndActivateCooldown();

                    if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
                    {
                        LOG_INFO("[CrossRegion] [第四阶段] 2.2.2.1 找到下一个信标 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                        // 重置信标配对错误状态
                        beacon_pairing_error_active_ = false;
                        LOG_INFO("[CrossRegion] [第四阶段] 信标配对正确，重置错误状态");

                        // 向定位发送信标id以跨区域
                        SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

                        if (mark_loc_result.detect_status == 2)
                        {
                            LOG_INFO("[CrossRegion] [第四阶段]  2.2.2.1.1 信标可以计算出位姿");
                            LOG_INFO("[CrossRegion] [第四阶段] 第一阶段完成");
                            phase_41_completed_ = true;
                        }
                    }
                    else
                    {
                        LOG_ERROR("[CrossRegion] [第四阶段] 2.2.2.2 下一对信标错误");
                        PublishZeroVelocity();

                        // 如果是首次发生配对错误，记录开始时间
                        if (!beacon_pairing_error_active_)
                        {
                            beacon_pairing_error_start_time_ = std::chrono::steady_clock::now();
                            beacon_pairing_error_active_ = true;
                            LOG_INFO("[CrossRegion] [第四阶段] 检测到信标配对错误，开始计时");
                        }
                        else
                        {
                            // 计算错误持续时间
                            auto current_time = std::chrono::steady_clock::now();
                            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                                current_time - beacon_pairing_error_start_time_);

                            // 如果错误持续超过20s，发布异常
                            if (duration.count() >= 20) // 5分钟 = 300秒
                            {
                                LOG_ERROR("[CrossRegion] [第四阶段] 信标配对错误持续超过20s，发布异常");
                                PublishException(SocExceptionLevel::ERROR,
                                                 SocExceptionValue::ALG_PNC_CROSS_ZONE_BEACON_PAIRING_EXCEPTION);
                                // 发布异常后重置错误状态
                                beacon_pairing_error_active_ = false;
                            }
                        }
                    }
                }
            }
        }
    }
}

void NavigationCrossRegionAlg::ResetAndActivateCooldown()
{
    // 重置冷却时间戳，并激活冷却机制
    // last_cooldown_time_ = std::chrono::steady_clock::now();
    // is_cooldown_active_ = true;
}

bool NavigationCrossRegionAlg::SetMarkLocationMarkId(int mark_id)
{
    if (set_mark_id_callback_)
    {
        return set_mark_id_callback_(mark_id);
    }
    return false;
}

void NavigationCrossRegionAlg::SetMarkLocationMarkIdCallback(std::function<bool(int)> callback)
{
    set_mark_id_callback_ = callback;
}

void NavigationCrossRegionAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    for (int i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            shortest_dis_inx = i;
        }
    }
}

void NavigationCrossRegionAlg::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // 冷却机制激活
    {
        LOG_INFO("[CrossRegion] 冷却机制激活");

        // 感知驱动冷却时间
        auto current_time = std::chrono::steady_clock::now();
        perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // 打印时间差
        LOG_INFO("[CrossRegion] 沿边感知驱动冷却的计时（秒）：({})", perception_drive_duration_.count());

        HandleEdgeCooldownMechanism(mark_loc_result, is_cooldown_active, perception_drive_duration_, perception_drive_cooldown_time_threshold_);
    }
    else // 冷却机制未激活
    {
        LOG_INFO("[CrossRegion] 冷却机制未激活");

        LOG_INFO("[CrossRegion] 开启感知驱动，同时关闭沿边!");

        EdgeFollowDisable();
        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                           std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // 感知检测结果
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[CrossRegion] 计时时间超过({})秒，冷却结束", perception_drive_cooldown_time_threshold);

        LOG_INFO("[CrossRegion] 开启感知驱动，同时关闭沿边");
        EdgeFollowDisable();
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // 冷却未结束，跳过执行
        LOG_INFO("[CrossRegion] 冷却时间未结束，未超过({})秒", perception_drive_cooldown_time_threshold);

        EdgeFollowEnable(); // 开启沿边
        LOG_INFO("[CrossRegion] 开启沿边");
    }
}

void NavigationCrossRegionAlg::HandleCrossRegionPerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // 冷却机制激活
    {
        LOG_INFO("[CrossRegion] 冷却机制激活");

        // 感知驱动冷却时间
        auto current_time = std::chrono::steady_clock::now();
        perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // 打印时间差
        LOG_INFO("[CrossRegion] 沿边感知驱动冷却的计时（秒）：({})", perception_drive_duration_.count());
        HandleCrossRegionCooldownMechanism(mark_loc_result, is_cooldown_active, perception_drive_duration_, perception_drive_cooldown_time_threshold_);
    }
    else // 冷却机制未激活
    {
        LOG_INFO("[CrossRegion] 冷却机制未激活");

        LOG_INFO("[CrossRegion] 开启感知驱动");

        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationCrossRegionAlg::HandleCrossRegionCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                                  std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // 感知检测结果
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[CrossRegion] 计时时间超过({})秒，冷却结束！", perception_drive_cooldown_time_threshold);

        LOG_INFO("[CrossRegion] 开启感知驱动");
        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // 冷却未结束，跳过执行
        LOG_INFO("[CrossRegion] 冷却时间未结束，未超过({})秒", perception_drive_cooldown_time_threshold);

        // 驱使小车走直线通过通道
        LOG_INFO("[CrossRegion] 驱使小车走直线通过通道");
        PublishVelocity(cross_region_linear_, 0); // 直走
    }
}

/**
 * @brief
 *
 * @param detect_status
 * @return true 原地旋转完成or检测到二维码
 * @return false 原地旋转没有完成
 */
bool NavigationCrossRegionAlg::ProcessPassageRotate(int mark_perception_status)
{
    bool ret = false;
    EdgeFollowDisable();

    // 计算原地旋转时间
    if (passage_rotate_time_ == 0 && passage_rotate_begin_time_ == 0)
    {
        passage_rotate_time_ = (2 * M_PI / cross_region_angular_) * 1000; // 转弯持续时间 ms
        passage_rotate_begin_time_ = GetTimestampMs();
    }

    if (mark_perception_status == 1) // 0 表示未感知（检测）到信标；1 表示感知（检测）到信标
    {
        passage_rotate_time_ = 0;
        passage_rotate_begin_time_ = 0;
        LOG_INFO("Passage rotate stage, find passage or QR code, stop rotate stage!");
        PublishVelocity(0, 0);
        return true;
    }

    // 执行旋转过程
    uint64_t run_time = GetTimestampMs() - passage_rotate_begin_time_;
    LOG_INFO("Passage rotate stage, run time {} rotate_time {}!", run_time, passage_rotate_time_);
    if (run_time < passage_rotate_time_)
    {
        PublishVelocity(0, cross_region_angular_); // 左旋转
        ret = false;
    }
    else
    {
        LOG_INFO("Recharge rotate stage finished!");
        passage_rotate_time_ = 0;
        passage_rotate_begin_time_ = 0;
        ret = true;
    }

    return ret;
}

/**
 * @brief Get the Precise Position Of Beacon object
 *
 * @param mark_loc_result
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @param is_get_precise_position 是否已经获取精确位姿
 * @note 驱使接近信标，直到在阈值范围内得到精确的二维码解算位姿
 */
void NavigationCrossRegionAlg::GetPrecisePositionOfBeacon(MarkLocationResult &mark_loc_result,
                                                          float &x_first, float &y_first, float &yaw_first,
                                                          bool &is_get_precise_position)
{
    // 若解算出，根据解算出的位姿，驱使小车到达指定的欧式距离范围内（近距离提高解算的精度）
    float x = mark_loc_result.xyzrpw.x;
    float y = mark_loc_result.xyzrpw.y;
    if (hypotf(x, y) > max_distance_threshold_)
    {
        // 驱使小车到达最大欧式距离范围内
        LOG_INFO("[CrossRegion] 驱使小车到达最大欧式距离范围内");
        PublishVelocity(cross_region_linear_, 0); // 前进
    }
    else if (hypotf(x, y) < min_distance_threshold_)
    {
        // 驱使小车到达最小欧式距离范围外
        LOG_INFO("[CrossRegion] 驱使小车到达最小欧式距离范围外");
        PublishVelocity(-cross_region_linear_, 0); // 倒车
    }
    else
    {
        PublishVelocity(0.0, 0.0, 100); // 停止小车100ms

        // 赋予x_first，y_first，yaw_first初始值
        x_first = mark_loc_result.xyzrpw.x;
        y_first = mark_loc_result.xyzrpw.y;
        yaw_first = mark_loc_result.xyzrpw.w;

        // 添加循环，使得在休眠期间继续获取最新的二维码位姿
        for (int i = 0; i < 30; ++i) // 每50ms获取一次新的位姿，总计2秒
        {
            bool is_pose_resolved = false; // 重新解算位姿是否成功

            {
                std::lock_guard<std::mutex> lck(mark_loc_mutex_);
                const float epsilon = 1e-6; // 容差范围
                if (std::fabs(mark_loc_result_law_.xyzrpw.x + 1.0) < epsilon &&
                    std::fabs(mark_loc_result_law_.xyzrpw.y + 1.0) < epsilon) // 信标原始值x,y都为-1，说明detect_status不为2，没有检测到
                {
                    LOG_INFO("[CrossRegion] 信标原始值x,y都为-1，说明detect_status不为2，没有检测到");
                    is_pose_resolved = false;
                }
                else
                {
                    LOG_INFO("[CrossRegion] 判断出detect_status为2, 检测到");
                    mark_loc_result = mark_loc_result_;
                    is_pose_resolved = true; // 重新解算位姿成功,执行坐标变换
                }
            }

            if (is_pose_resolved) // 执行坐标变换
            {
                x_first = mark_loc_result.xyzrpw.x;
                y_first = mark_loc_result.xyzrpw.y;
                yaw_first = mark_loc_result.xyzrpw.w;
            }

            LOG_INFO("[CrossRegion] 获取新的解算位姿 x_first = ({}) , y_first = ({}) , yaw_first = ({})",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // 休眠
            // std::this_thread::sleep_for(std::chrono::seconds(1));
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }

        is_get_precise_position = true; // 第二阶段完成
        LOG_INFO("[CrossRegion] 完成获取新的解算位姿 x_first = ({}) , y_first = ({}) , yaw_first = ({})",
                 x_first, y_first, Radians2Degrees(yaw_first));
    }
}

/**
 * @brief 信标在置信度范围外，对小车进行转向矫正
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::CorrectionOutsideConfidence(const MarkLocationResult &mark_loc_result)
{
    // 根据定位给出的信标方向进行旋转，找到高置信区域
    if (mark_loc_result.target_direction == static_cast<int>(Direction::LEFT_TURN)) // 左转向
    {
        PublishVelocity(0, cross_region_angular_);
        LOG_INFO("[CrossRegion] 信标出现在置信区域外! 向左转");
    }
    else if (mark_loc_result.target_direction == static_cast<int>(Direction::RIGHT_TURN)) // 右转向
    {
        PublishVelocity(0, -cross_region_angular_);
        LOG_INFO("[CrossRegion] 信标出现在置信区域外! 向右转");
    }
    else
    {
        LOG_INFO("[CrossRegion] 信标置信度区域检测失败");
    }
}

/**
 * @brief 沿边顺时针的情况下。根据信标调整到pass_point
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = channel_width_ / 2;
    // 判断小车在信标的左边还是右边。沿边顺时针
    if (y_first >= 0) // 在信标左边
    {
        // 判断小车在通道中心处的左边还是右边
        if (y_first <= pass_point) // 在通过点右边
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标左边，通过点右边，左转（右转）直行右转 y_first = {}, yaw_first = {}",
                     y_first, Radians2Degrees(yaw_first));
            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // 左转
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // 通过点右边。
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // 移动到通过点（0，pass_point）
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, -1);
                // 右转
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
        else // 在通过点左边
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标左边，通过点左边，左转（或右转）直行左转 y_first = {}, yaw_first = {}",
                     y_first, Radians2Degrees(yaw_first));
            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // 左转
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // 通过点右边。
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // 移动到通过点（0，channel_width_ / 2）
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // 向左转
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // 在信标右边
    {
        // 1. 首先，根据信标坐标，驱动小车到达其信标的左边
        if (x_first > adjust_mode_x_direction_threshold_) // 默认-0.5
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标右边，通过点右边，右转（左转）直行右转直线 x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // 在信标和通过点的右边，右转或左转
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);
                // 移动小车
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // 向右转
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
            else
            {
                // 在信标和通过点的右边，右转或左转
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
                // 移动小车到（x_first - adjust_mode_x_direction_threshold_，y_first）
                ControlLinearMotion(adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, 1);
                // 向右转
                ControlRotaryMotion(M_PI / 2, M_PI, cross_region_angular_);
                // 移动小车
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);
                // 向右转
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
        else // 在-adjust_mode_x_direction_threshold_范围外
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标右边，通过点右边，右转（左转）直行 x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            // 在信标和通过点的右边，右转或左转
            ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

            // 移动小车到（x_first，pass_point - y_first）
            ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

            // 向右转
            ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief 旋转运动控制
 *
 * @param yaw_des
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // 默认旋转方向 1.0左转 -1.0右转
    if (sign > 0)
    {
        LOG_INFO("[CrossRegion] 旋转方向：左转");
    }
    else
    {
        LOG_INFO("[CrossRegion] 旋转方向：右转");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // 转弯持续时间 ms
    LOG_INFO("[CrossRegion] 旋转角度 = {}", Radians2Degrees(ang_err));
    LOG_INFO("[CrossRegion] 角速度 = {}, 时间 = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

/**
 * @brief 直线运动控制
 *
 * @param pass_point
 * @param location
 */
void NavigationCrossRegionAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                                   const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_INFO("[CrossRegion] 直行距离 dis = {}", dis);
    LOG_INFO("[CrossRegion] 直行速度 = {}, 时间 = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

void NavigationCrossRegionAlg::ControlProcessToPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = 0.5; // 0.375 通过点：距离信标y轴方向的偏移量
    // float yaw_des = M_PI / 4;     // 45 期望调整到的角度
    // float yaw_compare = M_PI / 6; // 30度（用于比较，虽然当前未使用）

    // 使用一个局部变量保存当前角度，便于更新
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // 判断是否需要调整方向
    // 如果当前角度在 [0, 30°] 范围内，则目标角度为 45°（$$\\pi/4$$）
    if (current_yaw >= 0 && current_yaw <= M_PI / 6)
    {
        target_yaw = M_PI / 4;
        need_adjust = true;
    }
    // 如果当前角度在 [150°, 180°] 范围内，则目标角度为 135°（$$3\\pi/4$$）
    else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
    {
        target_yaw = 3 * M_PI / 4;
        need_adjust = true;
    }
    // 如果当前角度在 [0, -30°] 范围内，则目标角度为 -45°（$$-\\pi/4$$）
    else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
    {
        target_yaw = -M_PI / 4;
        need_adjust = true;
    }
    // 如果当前角度在 [-180°, -150°] 范围内，则目标角度为 -135°（$$-3\\pi/4$$）
    else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
    {
        target_yaw = -3 * M_PI / 4;
        need_adjust = true;
    }

    // 若需要调整方向，先执行旋转
    if (need_adjust)
    {
        LOG_INFO("[CrossRegion] 调整方向: 从 {} 调整到 {}",
                 Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
        ControlRotaryMotion(target_yaw, current_yaw, cross_region_angular_);
        // 假设旋转完成后，小车的方向更新
        current_yaw = target_yaw;
    }

    // 根据 y_first 判断小车相对于信标的位置，并确定向哪个通过点移动
    if (y_first <= 0) // 小车在信标右边
    {
        // 判断小车在通过点的左右
        if (y_first <= -pass_point) // 小车在通过点右边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 信标右边，且在通过点右边: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            // 计算目标点 B 的坐标，目标通过点的 y 坐标为 -pass_point
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            // 正向移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // 小车在通过点左边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 信标右边，且在通过点左边: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -pass_point, B_x, distance);
            // 倒车移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
    }
    else // 小车在信标左边
    {
        if (y_first <= pass_point) // 小车在通过点右边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 信标左边，且在通过点右边: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            // 计算目标点 B 的坐标，目标通过点的 y 坐标为 pass_point
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            // 倒车移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1);
        }
        else // 小车在通过点左边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 信标左边，且在通过点左边: y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, pass_point, B_x, distance);
            // 正向移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // 移动到通过点后，调整小车方向到 0 度
    LOG_INFO("[CrossRegion] 调整方向到 0 度");
    ControlRotaryMotion(0.0, current_yaw, cross_region_angular_);
}

/**
 * @brief 计算 B点的x坐标和A、B点的y坐标
 *
 * @param A_x
 * @param A_y
 * @param yaw
 * @param B_y
 * @param B_x
 * @param distance
 */
void NavigationCrossRegionAlg::ComputeB(float A_x, float A_y, float yaw, float B_y, float &B_x, float &distance)
{
    // 定义一个很小的常量，用于判断浮点数是否接近 0
    const float EPS = 1e-6;

    // 判断 sin(yaw) 是否足够大（不接近 0）
    if (std::fabs(std::sin(yaw)) > EPS)
    {
        // 计算参数 t
        float t = (B_y - A_y) / std::sin(yaw);
        // 根据 t 计算 B 的 x 坐标
        B_x = A_x + t * std::cos(yaw);
        // 欧式距离等于 |t|（单位方向向量）
        distance = std::fabs(t);
    }
    else
    {
        // 当 sin(yaw) 近似为 0 时，说明直线为水平直线
        // 此时，A 和 B 的 y 坐标必须相等，否则 B 不在直线上
        if (std::fabs(B_y - A_y) > EPS)
        {
            LOG_ERROR("[CrossRegion] 错误：对于水平直线，B的y坐标必须等于A的y坐标！");

            // 处理错误情况：这里将 B_x 设为 A_x，并将距离设为 0
            B_x = A_x;
            distance = 0;
        }
        else
        {
            // 当 A 与 B 的 y 坐标相同时，水平直线上 B 点的 x 坐标无法唯一确定
            // 此处默认取 B_x = A_x，距离为 0
            B_x = A_x;
            distance = 0;
        }
    }
}

/**
 * @brief 沿边逆时针的情况下。根据信标调整到pass_point
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @note 沿边是逆时针的情况
 */
void NavigationCrossRegionAlg::CounterClockwiseAlignPassPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float pass_point = -channel_width_ / 2;

    // 判断小车在信标的左边还是右边。沿边逆时针
    if (y_first <= 0) // 在信标右边
    {
        // 判断小车在通道中心处的左边还是右边
        if (y_first <= pass_point) // 在通过点右边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标右边，在通过点右边，左转（或右转）直行右转 y_first = {}, yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // 在通过点右边。向右转
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // 在通过点右边。向左转
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // 移动到通过点（0，pass_point）
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // 右转
                LOG_INFO("[CrossRegion] [第三阶段] 右转 旋转角度 = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
        else // 在通过点左边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标右边，在通过点左边，左转（右转）直行左转 y_first = {}, yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(pass_point - y_first) < dis_tolerance_)
            {
                // 在通过点左边。向右转
                ControlRotaryMotion(0.0 + cross_region_angle_compensation_, yaw_first, cross_region_angular_);
            }
            else
            {
                // 在通过点左边。向右转
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // 移动到通过点（0，pass_point）
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, -1); // 倒车

                // 向左转
                LOG_INFO("[CrossRegion] [第三阶段] 左转 旋转角度 = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(0.0, M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // 在信标左边
    {
        // 1. 首先，根据信标坐标，驱动小车到达其信标的右边
        if (x_first > adjust_mode_x_direction_threshold_) // 默认-adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标左边，在通过点左边，右转（左转）直行左转直线 x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // 在通过点左边。向右转
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

                // 移动小车
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // 向左转
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
            else
            {
                // 在信标和通过点的左边，右转或左转
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);

                // 移动小车
                ControlLinearMotion(adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, 1);

                // 向左转
                ControlRotaryMotion(-M_PI / 2, -M_PI, cross_region_angular_);

                // 移动小车
                ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

                // 向左转
                ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
            }
        }
        else // 在-adjust_mode_x_direction_threshold_范围外
        {
            LOG_INFO("[CrossRegion] [第三阶段] 信标左边，通过点左边，右转（左转）直行 x_first = {}, y_first = {}, yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // 在信标和通过点的左边，右转或左转
            ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

            // 移动小车
            ControlLinearMotion(pass_point, y_first, cross_region_linear_, 1);

            // 向左转
            ControlRotaryMotion(0.0, -M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief 判断是否从非草地到草地
 *
 * @param fusion_result
 * @return true
 * @return false
 */
bool NavigationCrossRegionAlg::Nongrass2Grass(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO("[CrossRegion] [Nongrass2Grass1] 正在处理从非草地到草地");

    // 将新状态加入队列
    frames_.push_back(fusion_result.grass_detecte_status);

    // 如果队列已满（超过10帧），则移除最早的一帧
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // 无草地（全是障碍物）
     * HAVE_GRASS_NO_OBSTACLE = 1,  // 有草地无障碍物 (全是草地)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // 有草地有障碍物 （部分草地部分障碍物）
     */

    if (frames_.size() >= 10)
    {
        size_t grass_count = 0; // 草地计数
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || status == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE) // 有草地无障碍物 (全是草地)
            {
                grass_count++; // 草地
            }
        }
        LOG_INFO("[CrossRegion] [Nongrass2Grass1] 草地计数 grass_count({})", grass_count);
        if (int(grass_count) > grass_count_threshold_) // 判断是否为草地
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // 小于10帧，无法判断
    return false;
}

/**
 * @brief 判断割草机跨区域是否结束，即是否经历了草地 → 非草地 → 草地的转变
 *
 * 思路说明：
 * 1. 使用双端队列 frames_ 存储最近若干帧的草地检测状态，队列长度超过 20 帧时自动丢弃最旧数据。
 * 2. 采用最近 $$window\_size$$（例如5）帧的投票方式判断当前是否为草地：
 *    - 若 $$grass\_count \geq nongrass\_count$$ 则认为当前为草地，否则为非草地。
 * 3. 状态机逻辑：
 *    - 如果当前状态为 IN_GRASS，当检测到当前为非草地时，切换到 CROSSING 状态。
 *    - 如果当前状态为 CROSSING，当检测到当前又为草地时，说明经历了跨区域操作（草地→非草地→草地），返回 true，并重置状态为 IN_GRASS。
 *
 * @param fusion_result 感知融合结果，其中 fusion_result.grass_detecte_status 表示当前的草地检测状态
 * @return true  表示检测到跨区域操作结束（即完成了草地→非草地→草地的转变）
 * @return false 表示未完成跨区域操作
 */
bool NavigationCrossRegionAlg::CrossRegionFinished(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO("[CrossRegion] [CrossRegionFinished] 正在处理草地 → 非草地 → 草地 跨区域判断");

    // 将当前检测状态加入队列
    frames_.push_back(fusion_result.grass_detecte_status);

    // 当队列长度超过20帧时，移除最旧的一帧
    if (frames_.size() > 20)
    {
        frames_.pop_front();
    }

    // 设定用于判断当前区域的窗口大小，例如取最近10帧数据
    const size_t window_size = 10;
    size_t effective_size = std::min(frames_.size(), window_size);
    size_t grass_count = 0;    // $$grass\_count$$：草地计数
    size_t nongrass_count = 0; // 非草地计数

    // 遍历最近 effective_size 帧数据进行投票判断
    for (size_t i = frames_.size() - effective_size; i < frames_.size(); ++i)
    {
        // 若检测状态为有草（包括有障碍物或无障碍物均认为是草地）
        if (frames_[i] == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || frames_[i] == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE)
        {
            grass_count++;
        }
        // 若检测状态为无草地
        else if (frames_[i] == GrassDetectStatus::NO_GRASS)
        {
            nongrass_count++;
        }
    }

    // 根据多数投票判断当前区域：若草地计数较多，则认为当前为草地
    bool current_is_grass = (grass_count >= nongrass_count);
    LOG_INFO("[CrossRegion] 最近 ({}) 帧: grass_count = ({}), nongrass_count = ({}), current_is_grass = ({})",
             effective_size, grass_count, nongrass_count, current_is_grass ? "true" : "false");

    // 状态机逻辑实现：
    // 1. 初始状态为 IN_GRASS，当检测到当前为非草地时，说明割草机已离开草地区域，切换到 CROSSING 状态。
    // 2. 当处于 CROSSING 状态下，若检测到当前区域又为草地，则认为跨区域操作完成。
    if (current_state_ == RegionState::IN_GRASS)
    {
        if (!current_is_grass)
        {
            current_state_ = RegionState::CROSSING;
            LOG_INFO("[CrossRegion] 状态由 IN_GRASS 转为 CROSSING");
        }
    }
    else if (current_state_ == RegionState::CROSSING)
    {
        if (current_is_grass)
        {
            // 跨区域完成：经历了草地 → 非草地 → 草地
            LOG_INFO("[CrossRegion] 检测到跨区域结束（非草地返回草地）");
            // 重置状态为 IN_GRASS 以便下次检测
            current_state_ = RegionState::IN_GRASS;
            // 清空队列以防止历史数据干扰后续判断
            frames_.clear();
            return true;
        }
    }

    // 如果状态未满足完成跨区域的条件，则返回 false
    return false;
}

/**
 * @brief 以PassPoints的视角来看
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ControlProcessToEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = 0.5; // 0.375 通过点：距离信标y轴方向的偏移量
    // float yaw_des = M_PI / 4;     // 45 期望调整到的角度
    // float yaw_compare = M_PI / 6; // 30度（用于比较，虽然当前未使用）

    // 使用一个局部变量保存当前角度，便于更新
    float current_yaw = yaw_first;
    bool need_adjust = false;
    float target_yaw = current_yaw;

    // 判断是否需要调整方向
    // 如果当前角度在 [0, 30°] 范围内，则目标角度为 45°（$$\\pi/4$$）
    if (current_yaw >= 0 && current_yaw <= M_PI / 6)
    {
        target_yaw = M_PI / 4;
        need_adjust = true;
    }
    // 如果当前角度在 [150°, 180°] 范围内，则目标角度为 135°（$$3\\pi/4$$）
    else if (current_yaw >= 5 * M_PI / 6 && current_yaw <= M_PI)
    {
        target_yaw = 3 * M_PI / 4;
        need_adjust = true;
    }
    // 如果当前角度在 [0, -30°] 范围内，则目标角度为 -45°（$$-\\pi/4$$）
    else if (current_yaw >= -M_PI / 6 && current_yaw < 0)
    {
        target_yaw = -M_PI / 4;
        need_adjust = true;
    }
    // 如果当前角度在 [-180°, -150°] 范围内，则目标角度为 -135°（$$-3\\pi/4$$）
    else if (current_yaw <= -5 * M_PI / 6 && current_yaw >= -M_PI)
    {
        target_yaw = -3 * M_PI / 4;
        need_adjust = true;
    }

    // 若需要调整方向，先执行旋转
    if (need_adjust)
    {
        LOG_INFO("[CrossRegion] 调整方向: 从 {} 调整到 {}",
                 Radians2Degrees(current_yaw), Radians2Degrees(target_yaw));
        ControlRotaryMotion(target_yaw, current_yaw, cross_region_angular_);
        // 假设旋转完成后，小车的方向更新
        current_yaw = target_yaw;
    }

    // 判断小车在信标的左边还是右边
    if (y_first <= 0) // 在信标左边
    {
        // 判断小车在通过点的左边还是右边
        if (y_first <= -end_point) // 在通过点左边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标左边，在通过点左边 y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance); // 小车的位置作为A点，通过点作为B点

            // 移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
        else // 在通过点右边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标左边，在通过点右边 y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, -end_point, B_x, distance); // 小车的位置作为A点，通过点作为B点

            // 移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1); // 倒车
        }
    }
    else // 在信标右边
    {
        // 判断小车在通过点的左边还是右边
        if (y_first <= end_point) // 在通过点左边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标右边，在通过点左边 y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance); // 小车的位置作为A点，通过点作为B点

            // 移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, -1); // 倒车
        }
        else // 在通过点右边
        {
            LOG_INFO("[CrossRegion] [第三阶段] 在信标右边，在通过点右边 y_first = {}, current_yaw = {}",
                     y_first, Radians2Degrees(current_yaw));

            float B_x = 0.0;
            float distance = 0.0;
            ComputeB(x_first, y_first, current_yaw, end_point, B_x, distance); // 小车的位置作为A点，通过点作为B点

            // 移动到通过点
            ControlLinearMotion(distance, 0.0, cross_region_linear_, 1);
        }
    }

    // 移动到通过点后，调整小车方向到 0 度
    LOG_INFO("[CrossRegion] 调整方向到 M_PI 度");
    ControlRotaryMotion(M_PI, current_yaw, cross_region_angular_);
}

/**
 * @brief 沿边逆时针的情况下。根据信标调整到end_point + channel_stop_pose_x_
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 * @note 1.沿边是逆时针的情况  2. 基准坐标系为信标
 */
void NavigationCrossRegionAlg::CounterClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = -channel_width_ / 2;

    // 判断小车在信标的左边还是右边。沿边逆时针
    if (y_first <= 0) // 在信标右边
    {
        // 判断小车在通道中心处的左边还是右边
        if (y_first <= end_point) // 在通过点右边
        {
            LOG_INFO("[CrossRegion] [第四阶段] 在信标右边，在通过点右边，左转（或右转）直行左转 y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // 在通过点右边。向右转
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // 在通过点右边。向右转
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // 移动到通过点（0，end_point）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // 左转
                LOG_INFO("[CrossRegion] [第四阶段] 左转 旋转角度 = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
        else // 在通过点左边
        {
            LOG_INFO("[CrossRegion] [第四阶段] 在信标右边，在通过点左边，左转（右转）直行右转 y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // 在通过点右边。向右转
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // 在通过点左边。向左转
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // 移动到通过点（0，end_point）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, -1); // 倒车

                // 向右转
                LOG_INFO("[CrossRegion] [第四阶段] 右转 旋转角度 = {}", Radians2Degrees(M_PI / 2));
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // 在信标左边
    {
        // 1. 首先，根据信标坐标，驱动小车到达其信标的右边
        if (x_first < -adjust_mode_x_direction_threshold_) // 默认正adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion] [第四阶段] 在信标左边，在通过点左边，右转（左转）直行右转直线 x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            if (fabsf(-adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // 向左转
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

                // 移动小车到（x_first，y_first - pass_point）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // 向右转
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
            else
            {
                // 在信标和通过点的左边，右转或左转
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);

                // 移动小车到（x_first - （-adjust_mode_x_direction_threshold_），y_first）
                ControlLinearMotion(-adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, -1);

                // 向右转
                ControlRotaryMotion(-M_PI / 2, -M_PI, cross_region_angular_);

                // 移动小车到（x_first - （-adjust_mode_x_direction_threshold_），y_first - pass_point）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // 向右转
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
        else // 在正0.5范围外
        {
            LOG_INFO("[CrossRegion] [第四阶段] 信标左边，通过点左边，右转（左转）直行 x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));

            // 在信标和通过点的左边，右转或左转
            ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);

            // 移动小车到（x_first，y_first - pass_point）
            ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

            // 向右转
            ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief 沿边顺时针的情况下。根据信标调整到end_point + channel_stop_pose_x_
 *
 * @param x_first
 * @param y_first
 * @param yaw_first
 */
void NavigationCrossRegionAlg::ClockwiseAlignEndPoints(const float &x_first, const float &y_first, const float &yaw_first)
{
    float end_point = channel_width_ / 2;
    // 判断小车在信标的左边还是右边。沿边顺时针
    if (y_first >= 0) // 在信标左边
    {
        // 判断小车在通道中心处的左边还是右边
        if (y_first <= end_point) // 在通过点右边
        {
            LOG_INFO("[CrossRegion]第四阶段！ 信标左边，通过点右边，左转（右转）直行左转 y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));

            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // 通过点右边。向右转
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // 通过点右边。
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // 移动到通过点（0，end_point）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, -1);
                // 左转
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
        else // 在通过点左边
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标左边，通过点左边，左转（或右转）直行右转 y_first = {} , yaw_first = {}", y_first, Radians2Degrees(yaw_first));
            if (fabsf(end_point - y_first) < dis_tolerance_)
            {
                // 通过点右边。向右转
                ControlRotaryMotion(-M_PI, yaw_first, cross_region_angular_);
            }
            else
            {
                // 通过点右边。
                ControlRotaryMotion(-M_PI / 2, yaw_first, cross_region_angular_);
                // 移动到通过点（0，channel_width_ / 2）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);
                // 向右转
                ControlRotaryMotion(-M_PI, -M_PI / 2, cross_region_angular_);
            }
        }
    }
    else // 在信标右边
    {
        // 1. 首先，根据信标坐标，驱动小车到达其信标的左边
        if (x_first < -adjust_mode_x_direction_threshold_) // 默认adjust_mode_x_direction_threshold_
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标右边，通过点右边，右转（左转）直行左转直线左转 x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            if (fabsf(adjust_mode_x_direction_threshold_ - x_first) < dis_tolerance_)
            {
                // 向左转
                ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);

                // 移动小车到（x_first - adjust_mode_x_direction_threshold_，pass_point - y_first）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // 向左转
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
            else
            {
                // 在信标和通过点的右边，右转或左转
                ControlRotaryMotion(M_PI, yaw_first, cross_region_angular_);

                // 移动小车到（x_first - adjust_mode_x_direction_threshold_，y_first）
                ControlLinearMotion(-adjust_mode_x_direction_threshold_, x_first, cross_region_linear_, -1);

                // 向左转
                ControlRotaryMotion(M_PI / 2, M_PI, cross_region_angular_);

                // 移动小车到（x_first - adjust_mode_x_direction_threshold_，pass_point - y_first）
                ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);

                // 向左转
                ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
            }
        }
        else // 在-0.5范围外
        {
            LOG_INFO("[CrossRegion]第三阶段！ 信标右边，通过点右边，右转（左转）直行 x_first = {} , y_first = {} , yaw_first = {}",
                     x_first, y_first, Radians2Degrees(yaw_first));
            // 在信标和通过点的右边，右转或左转
            ControlRotaryMotion(M_PI / 2, yaw_first, cross_region_angular_);
            // 移动小车到（x_first，pass_point - y_first）
            ControlLinearMotion(end_point, y_first, cross_region_linear_, 1);
            // 向左转
            ControlRotaryMotion(M_PI, M_PI / 2, cross_region_angular_);
        }
    }
}

/**
 * @brief 基于感知对方向进行调整
 *
 * @param mark_loc_result
 */
void NavigationCrossRegionAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // 偏左，左转
        LOG_INFO("基于感知向左调整方向!");
        PublishVelocity(0, cross_region_special_angular_);
        break;

    case 0: // 居中，直线
        LOG_INFO("基于感知直线调整方向!");
        PublishVelocity(cross_region_special_linear_, 0);
        break;

    case 1: // 偏右，右转
        LOG_INFO("基于感知向右调整方向!");
        PublishVelocity(0, -cross_region_special_angular_);
        break;

    default:
        LOG_INFO("基于感知mark_perception_direction标志位错误!");
        PublishVelocity(cross_region_special_linear_, 0);
        break;
    }
}

/**
 * @brief 对正整数进行配对
 *
 * 规则说明：
 * 如果输入的正整数 n 为奇数，则返回 n+1；
 * 如果 n 为偶数，则返回 n-1。
 * 公式表示如下：
 * $$\text{若 } n \% 2 == 1, \text{ 则输出 } n+1$$
 * $$\text{若 } n \% 2 == 0, \text{ 则输出 } n-1$$
 *
 * @param n 正整数
 * @return int 配对后的数值
 */
int NavigationCrossRegionAlg::PairNumber(int n)
{
    // 判断 n 是否为奇数
    if (n % 2 == 1)
    {
        // 如果 n 为奇数，返回 n+1
        return n + 1;
    }
    else
    {
        // 如果 n 为偶数，返回 n-1
        return n - 1;
    }
}

// 修改后的安全移动控制函数
void NavigationCrossRegionAlg::SafeLinearMotion(float target_dis, float current_dis,
                                                float vel_linear, int reverse,
                                                MarkLocationResult &mark_loc_result)
{
    const float step = 0.01f; // 每次移动1cm后做检测
    float remaining = fabs(target_dis - current_dis);
    front_beacon_detected_ = false;

    while (remaining > 0 && !emergency_stop_)
    {
        LOG_INFO("[SafeLinearMotion] 安全移动控制执行");

        // 每次移动前检查最新信标状态
        mark_loc_result = mark_loc_result_;
        CheckFrontBeaconCollision(mark_loc_result);

        if (front_beacon_detected_)
        {
            LOG_WARN("[SafeLinearMotion] 检测到前方有效信标, 中断直行流程");
            emergency_stop_ = true;
            non_grass_area_reached_ = true; // 强制进入非草地处理流程

            PublishVelocity(0.0, 0.0, 100);
            break;
        }

        // 执行小段移动
        float move_dis = std::min(remaining, step);
        uint64_t t = (move_dis / vel_linear) * 1000;
        PublishVelocity(reverse * vel_linear, 0, t);
        remaining -= move_dis;

        LOG_INFO("[SafeLinearMotion] 移动完成 | 新剩余距离: {:.2f}m", remaining);
    }
}

// 新增碰撞检测逻辑
void NavigationCrossRegionAlg::CheckFrontBeaconCollision(const MarkLocationResult &mark_loc_result)
{
    // 有效性检查
    if (mark_loc_result.mark_id_distance.empty())
    {
        LOG_INFO("[CheckFrontBeaconCollision] mark_id_distance 为空");
        front_beacon_detected_ = false;
        return;
    }

    // 判断信标是否有效。（mark_id_distance小于50cm认为当前信标有效）
    int shortest_dis_inx = -1;
    std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
    FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

    if (shortest_dis_inx == -1) // 若信标无效。不操作，继续之前的动作
    {
        LOG_INFO("[CheckFrontBeaconCollision] mark_id_distance 无效");
        front_beacon_detected_ = false;
        return;
    }
    else // 若信标有效
    {
        LOG_INFO("[CheckFrontBeaconCollision] mark_id_distance 有效");
        LOG_INFO("[CheckFrontBeaconCollision] 有效的信标为 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

        if (next_paired_beacon_id_ == mark_id_distance_vec[shortest_dis_inx].mark_id)
        {
            LOG_INFO("[CheckFrontBeaconCollision] 找到下一个信标 mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

            // 向定位发送信标id以跨区域
            SetMarkLocationMarkId(mark_id_distance_vec[shortest_dis_inx].mark_id);

            if (mark_loc_result.detect_status == 2)
            {
                LOG_INFO("[CheckFrontBeaconCollision] 信标可以计算出位姿");

                front_beacon_detected_ = true;
                return;
            }
        }
        else
        {
            LOG_ERROR("[CheckFrontBeaconCollision] 下一对信标错误");
        }
    }

    front_beacon_detected_ = false;
}

void NavigationCrossRegionAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_cross_region_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation cross_region publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

void NavigationCrossRegionAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

} // namespace fescue_iox
