# Filter Comparison Plot Script

## Overview
This script (`plot_filter_comparison.py`) creates comparison plots showing the effectiveness of filtering on IMU data from the stuck recovery system. It generates a two-column plot comparing raw vs filtered data for both angular velocity and linear acceleration.

## Features
- **Two-column layout**: Angular velocity comparison (left) and linear acceleration comparison (right)
- **Statistical analysis**: Shows noise reduction percentages and standard deviations
- **English annotations**: All labels, titles, and statistics are in English
- **High-quality output**: Saves plots as high-resolution PNG files (300 DPI)
- **Interactive mode**: Optional interactive display for development/analysis
- **Data summary**: Prints comprehensive statistics about the dataset

## Usage

### Basic Usage (Save to PNG only)
```bash
python3 scripts/plot_filter_comparison.py
```

### Interactive Mode (Show plot window)
```bash
python3 scripts/plot_filter_comparison.py --interactive
# or
python3 scripts/plot_filter_comparison.py -i
```

### Help
```bash
python3 scripts/plot_filter_comparison.py --help
# or
python3 scripts/plot_filter_comparison.py -h
```

## Input Data
The script reads from `scripts/stuck_recovery_filter_data.csv` which should contain:
- `timestamp`: Timestamp in milliseconds
- `raw_angular_velocity`: Unfiltered angular velocity (rad/s)
- `filtered_angular_velocity`: Filtered angular velocity (rad/s)
- `raw_linear_acceleration`: Unfiltered linear acceleration (m/s²)
- `filtered_linear_acceleration`: Filtered linear acceleration (m/s²)

## Output
- **PNG file**: `scripts/filter_comparison_plot.png` (high-resolution comparison plots)
- **Console output**: Data summary with statistics and noise reduction metrics

## Plot Features
- **Left plot**: Angular velocity comparison with noise reduction statistics
- **Right plot**: Linear acceleration comparison with noise reduction statistics
- **Color coding**: 
  - Raw data: Red (angular velocity), Orange (linear acceleration)
  - Filtered data: Blue (angular velocity), Green (linear acceleration)
- **Statistics boxes**: Show standard deviation and noise reduction percentage
- **Grid lines**: Improve readability
- **Professional styling**: Clear labels, legends, and formatting

## Dependencies
- pandas
- matplotlib
- numpy

## Example Output Statistics
```
Total data points: 10716
Time range: 0.00 to 143.31 seconds
Duration: 143.31 seconds

Angular Velocity Statistics:
  Raw - Mean: -0.000212, Std: 0.005299
  Filtered - Mean: -0.000212, Std: 0.004002

Linear Acceleration Statistics:
  Raw - Mean: -0.022018, Std: 0.045225
  Filtered - Mean: -0.021972, Std: 0.032023
```

The script demonstrates significant noise reduction:
- Angular velocity: ~24.5% noise reduction
- Linear acceleration: ~29.2% noise reduction
