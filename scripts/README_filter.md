# IMU数据滤波系统

## 概述

本系统为脱困检测恢复模块添加了IMU数据滤波功能，对角速度(angular_velocity_z)和线性加速度(linear_acceleration_x)进行低通滤波处理，以减少传感器噪声对运动检测的影响。

## 滤波器设计

### 滤波器类型
- **一阶低通滤波器 (First-order Low-pass Filter)**
- 公式: `y[n] = α * x[n] + (1 - α) * y[n-1]`
- 其中：
  - `x[n]`: 当前输入值
  - `y[n]`: 当前输出值
  - `y[n-1]`: 上一次输出值
  - `α`: 滤波系数 (0 < α < 1)

### 滤波器参数
- **默认滤波系数**: α = 0.1
- **特性**:
  - α 越小：滤波越强，噪声抑制越好，但响应越慢
  - α 越大：响应越快，但滤波效果越弱
  - α = 0.1 提供了良好的噪声抑制和合理的响应速度平衡

### 为什么选择低通滤波器
1. **IMU噪声特性**: IMU传感器通常包含高频噪声
2. **运动信号特性**: 草坪机的运动信号主要在低频范围
3. **计算效率**: 一阶低通滤波器计算简单，实时性好
4. **稳定性**: 滤波器稳定，不会引入振荡

## 代码修改说明

### 头文件修改 (stuck_detection_recovery.hpp)
```cpp
// 新增滤波器相关成员变量
float filter_alpha_{0.1f};                 // 滤波器系数
float filtered_angular_velocity_{0.0f};    // 滤波后的角速度
float filtered_linear_acceleration_{0.0f}; // 滤波后的线性加速度
bool filter_initialized_{false};           // 滤波器是否已初始化

// 新增滤波函数声明
float ApplyLowPassFilter(float new_value, float &filtered_value, float alpha);
void InitializeFilters(float initial_angular_velocity, float initial_linear_acceleration);
void LogFilteringData(uint64_t timestamp, float raw_angular_vel, float filtered_angular_vel, 
                     float raw_linear_accel, float filtered_linear_accel);
```

### 源文件修改 (stuck_detection_recovery.cpp)
1. **ProcessImuData函数**: 在零偏校正后添加滤波处理
2. **数据记录**: 同时记录滤波前后的数据
3. **滤波器实现**: 添加滤波函数实现

## 数据记录

### 滤波数据文件
- **路径**: `/userdata/log/stuck_recovery_filter_data.csv`
- **格式**: CSV文件，包含以下列：
  - `timestamp`: 时间戳 (毫秒)
  - `raw_angular_velocity`: 原始角速度 (rad/s)
  - `filtered_angular_velocity`: 滤波后角速度 (rad/s)
  - `raw_linear_acceleration`: 原始线性加速度 (m/s²)
  - `filtered_linear_acceleration`: 滤波后线性加速度 (m/s²)

## 可视化工具

### 1. 实际数据可视化 (visualize_filter_data.py)
```bash
# 基本用法
python3 scripts/visualize_filter_data.py

# 指定输入文件和输出目录
python3 scripts/visualize_filter_data.py -i /path/to/filter_data.csv -o ./output/

# 包含频域分析
python3 scripts/visualize_filter_data.py -f
```

**功能**:
- 时域对比图：显示滤波前后的数据对比
- 噪声分析：显示被滤除的噪声成分
- 频域分析：显示滤波器的频率响应特性
- 统计信息：计算噪声降低百分比

### 2. 滤波演示 (test_filter_demo.py)
```bash
python3 scripts/test_filter_demo.py
```

**功能**:
- 生成模拟IMU数据
- 演示滤波效果
- 提供滤波器性能分析

## 使用步骤

### 1. 编译和运行
```bash
# 编译项目
cd /path/to/project
make

# 运行草坪机导航系统
# 系统会自动启用滤波功能并记录数据
```

### 2. 数据分析
```bash
# 等待系统运行一段时间后，查看滤波数据
python3 scripts/visualize_filter_data.py

# 或者先运行演示了解滤波效果
python3 scripts/test_filter_demo.py
```

### 3. 参数调整
如需调整滤波器参数，修改 `stuck_detection_recovery.hpp` 中的 `filter_alpha_` 值：
- 增大 α (如 0.2): 响应更快，但滤波效果减弱
- 减小 α (如 0.05): 滤波更强，但响应更慢

## 滤波效果评估

### 评估指标
1. **噪声降低百分比**: (1 - σ_filtered/σ_raw) × 100%
2. **信号延迟**: 滤波引入的时间延迟
3. **频率响应**: 不同频率成分的衰减情况

### 预期效果
- **噪声降低**: 通常可降低60-80%的高频噪声
- **信号保真度**: 保持低频运动信号的完整性
- **实时性**: 滤波延迟通常小于100ms

## 故障排除

### 常见问题
1. **数据文件不存在**: 确保脱困检测系统已启动并运行
2. **Python依赖缺失**: 安装必要的Python包
   ```bash
   pip3 install pandas matplotlib numpy
   ```
3. **中文字体显示问题**: 系统会自动回退到英文字体

### 调试建议
1. 检查日志输出中的滤波器初始化信息
2. 验证数据文件是否正常生成
3. 使用演示脚本验证可视化工具是否正常工作

## 技术细节

### 滤波器特性
- **截止频率**: 约为 α/(2π) × 采样频率
- **相位延迟**: 低频信号几乎无延迟
- **稳定性**: 无条件稳定 (0 < α < 1)

### 性能影响
- **计算开销**: 每个数据点仅需1次乘法和1次加法
- **内存开销**: 仅需存储1个历史值
- **实时性**: 对系统实时性影响极小

## 扩展功能

### 可能的改进
1. **自适应滤波**: 根据运动状态动态调整α值
2. **多阶滤波**: 使用二阶或更高阶滤波器
3. **卡尔曼滤波**: 结合运动模型的更高级滤波
4. **频域滤波**: 使用FFT实现更精确的频域滤波

### 参数优化
可以根据实际使用场景调整滤波参数：
- **高速运动**: 增大α值以提高响应速度
- **精确定位**: 减小α值以获得更好的噪声抑制
- **不同传感器**: 根据传感器特性调整参数
