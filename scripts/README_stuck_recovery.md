# Stuck Recovery Data Visualization Tool

## Overview
This script visualizes stuck recovery data from the lawn mower's stuck detection and recovery system. It provides comprehensive analysis of movement patterns, displacement, and velocity data.

## Features
- **Main Analysis Plot**: Displays linear/angular displacement and velocity over time
- **Cumulative Displacement Analysis**: Shows cumulative movement patterns
- **Statistical Summary**: Provides detailed statistics about the data
- **English Interface**: All labels and messages are in English
- **High-Quality Output**: Saves plots as high-resolution PNG files

## Usage

### Basic Usage
```bash
python3 stuck_recovery_visualize_filter_data.py <input_csv_file>
```

### Advanced Usage
```bash
python3 stuck_recovery_visualize_filter_data.py <input_csv_file> --output_dir <output_directory> --cumulative
```

### Parameters
- `input_file`: Path to the CSV file containing stuck recovery data (required)
- `--output_dir`: Directory to save the generated plots (optional, default: current directory)
- `--cumulative` or `-c`: Generate additional cumulative displacement analysis plot (optional)

## Input Data Format
The CSV file should contain the following columns:
- `timestamp`: Time in milliseconds
- `linear_displacement`: Linear displacement in meters
- `angular_displacement`: Angular displacement in radians
- `linear_velocity`: Linear velocity in m/s
- `angular_velocity`: Angular velocity in rad/s

Example CSV format:
```csv
timestamp,linear_displacement,angular_displacement,linear_velocity,angular_velocity
1000,0.001,0.002,0.05,0.1
1100,0.002,0.003,0.06,0.12
...
```

## Output Files
The script generates the following files:
1. `stuck_recovery_analysis_YYYYMMDD_HHMMSS.png`: Main analysis plot with 4 subplots
2. `cumulative_displacement_YYYYMMDD_HHMMSS.png`: Cumulative displacement analysis (if --cumulative is used)

## Plot Descriptions

### Main Analysis Plot
- **Linear Displacement**: Shows instantaneous linear displacement over time
- **Angular Displacement**: Shows instantaneous angular displacement over time
- **Linear Velocity**: Shows linear velocity over time
- **Angular Velocity**: Shows angular velocity over time

### Cumulative Displacement Plot
- **Cumulative Linear Displacement**: Shows total accumulated linear movement
- **Cumulative Angular Displacement**: Shows total accumulated angular movement

## Statistics Output
The script prints the following statistics:
- Total linear displacement (m)
- Total angular displacement (rad)
- Average linear velocity (m/s)
- Average angular velocity (rad/s)
- Maximum linear velocity (m/s)
- Maximum angular velocity (rad/s)
- Data duration (seconds)
- Number of data points
- Sampling rate (Hz)

## Examples

### Example 1: Basic visualization
```bash
python3 stuck_recovery_visualize_filter_data.py /userdata/log/stuck_recovery_data.csv
```

### Example 2: Full analysis with custom output directory
```bash
python3 stuck_recovery_visualize_filter_data.py /userdata/log/stuck_recovery_data.csv --output_dir ./analysis_results --cumulative
```

## Dependencies
- Python 3.x
- pandas
- matplotlib
- numpy

## Notes
- The script automatically handles time conversion from milliseconds to seconds
- All plots are saved with high DPI (300) for publication quality
- The script uses `plt.close()` instead of `plt.show()` to avoid GUI dependencies
- Error handling is included for missing files and invalid data formats
